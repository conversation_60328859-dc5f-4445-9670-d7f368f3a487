#!/usr/bin/env python3
"""
简化的排班生成功能测试
直接测试API和前端功能
"""
import requests
import json
import time

def test_api_chat_mode():
    """测试API对话模式"""
    print("🤖 测试API对话模式...")
    
    # 登录获取token
    login_response = requests.post('http://localhost:5000/api/v1/auth/login', 
                                  json={'username': 'admin', 'password': 'admin123'})
    
    if login_response.status_code != 200:
        print(f"❌ 登录失败: {login_response.status_code}")
        return False
    
    token = login_response.json()['data']['token']
    headers = {'Authorization': f'Bearer {token}', 'Content-Type': 'application/json'}
    print("✅ 登录成功")
    
    # 测试对话模式API
    chat_data = {
        'department_id': 2,  # 西区
        'requirements': '请为西区安排一周排班，每天需要责任护士2名，值班护士1名。请用友好的语气回复，并提供Markdown表格格式的排班结果。',
        'schedule_name': '对话模式测试排班表',
        'start_date': '2025-07-28',
        'end_date': '2025-08-03',
        'model_id': 'openrouter',  # 使用Kimi K2模型
        'chat_mode': True
    }
    
    try:
        response = requests.post('http://localhost:5000/api/v1/schedules/generate-stream', 
                               headers=headers, json=chat_data, stream=True, timeout=60)
        
        if response.status_code == 200:
            print("✅ 对话模式API调用成功")
            
            full_content = ""
            markdown_found = False
            table_found = False
            
            # 处理流式响应
            for line in response.iter_lines():
                if line:
                    line_str = line.decode('utf-8')
                    if line_str.startswith('data: '):
                        data_str = line_str[6:]
                        if data_str == '[DONE]':
                            break
                        try:
                            data = json.loads(data_str)
                            if 'choices' in data:
                                content = data['choices'][0]['delta'].get('content', '')
                                if content:
                                    full_content += content
                                    
                                    # 检查Markdown特征
                                    if '#' in content or '**' in content or '*' in content:
                                        markdown_found = True
                                    
                                    # 检查表格特征
                                    if '|' in content and '-' in content:
                                        table_found = True
                        except json.JSONDecodeError:
                            continue
            
            print(f"✅ 生成内容长度: {len(full_content)} 字符")
            print(f"✅ 包含Markdown格式: {markdown_found}")
            print(f"✅ 包含表格格式: {table_found}")
            
            # 显示部分内容
            if full_content:
                preview = full_content[:200] + "..." if len(full_content) > 200 else full_content
                print(f"📝 内容预览: {preview}")
            
            return True
            
        else:
            print(f"❌ 对话模式API调用失败: {response.status_code}")
            return False
    
    except Exception as e:
        print(f"❌ 对话模式测试异常: {str(e)}")
        return False

def test_api_table_mode():
    """测试API表格模式"""
    print("\n📊 测试API表格模式...")
    
    # 登录获取token
    login_response = requests.post('http://localhost:5000/api/v1/auth/login', 
                                  json={'username': 'admin', 'password': 'admin123'})
    
    token = login_response.json()['data']['token']
    headers = {'Authorization': f'Bearer {token}', 'Content-Type': 'application/json'}
    
    # 测试表格模式API
    table_data = {
        'department_id': 2,  # 西区
        'requirements': '请为西区安排一周排班，每天需要责任护士2名，值班护士1名。请提供详细的排班表格和JSON格式数据。',
        'schedule_name': '表格模式测试排班表',
        'start_date': '2025-07-28',
        'end_date': '2025-08-03',
        'model_id': 'siliconflow',  # 使用Qwen模型
        'table_mode': True
    }
    
    try:
        response = requests.post('http://localhost:5000/api/v1/schedules/generate-stream', 
                               headers=headers, json=table_data, stream=True, timeout=60)
        
        if response.status_code == 200:
            print("✅ 表格模式API调用成功")
            
            full_content = ""
            json_found = False
            table_found = False
            
            # 处理流式响应
            for line in response.iter_lines():
                if line:
                    line_str = line.decode('utf-8')
                    if line_str.startswith('data: '):
                        data_str = line_str[6:]
                        if data_str == '[DONE]':
                            break
                        try:
                            data = json.loads(data_str)
                            if 'choices' in data:
                                content = data['choices'][0]['delta'].get('content', '')
                                if content:
                                    full_content += content
                                    
                                    # 检查JSON特征
                                    if '```json' in content or '"schedule"' in content:
                                        json_found = True
                                    
                                    # 检查表格特征
                                    if '|' in content and '护士' in content:
                                        table_found = True
                        except json.JSONDecodeError:
                            continue
            
            print(f"✅ 生成内容长度: {len(full_content)} 字符")
            print(f"✅ 包含JSON格式: {json_found}")
            print(f"✅ 包含表格格式: {table_found}")
            
            # 尝试提取JSON数据
            if '```json' in full_content:
                try:
                    json_start = full_content.find('```json') + 7
                    json_end = full_content.find('```', json_start)
                    if json_end > json_start:
                        json_str = full_content[json_start:json_end].strip()
                        schedule_data = json.loads(json_str)
                        print(f"✅ 成功解析JSON数据，包含 {len(schedule_data.get('schedule', {}))} 个护士的排班")
                except:
                    print("⚠️ JSON数据解析失败")
            
            return True
            
        else:
            print(f"❌ 表格模式API调用失败: {response.status_code}")
            return False
    
    except Exception as e:
        print(f"❌ 表格模式测试异常: {str(e)}")
        return False

def test_model_config():
    """测试模型配置"""
    print("\n🔧 测试模型配置...")
    
    # 登录获取token
    login_response = requests.post('http://localhost:5000/api/v1/auth/login', 
                                  json={'username': 'admin', 'password': 'admin123'})
    
    token = login_response.json()['data']['token']
    headers = {'Authorization': f'Bearer {token}'}
    
    # 获取可用模型
    models_response = requests.get('http://localhost:5000/api/v1/models/enabled', headers=headers)
    
    if models_response.status_code == 200:
        models = models_response.json()['data']
        print(f"✅ 获取到 {len(models)} 个可用模型:")
        
        for model in models:
            status = "✅ 启用" if model['enabled'] else "❌ 禁用"
            default = " (默认)" if model.get('is_default') else ""
            print(f"  - {model['name']}: {model['provider']} - {status}{default}")
        
        return len(models) > 0
    else:
        print(f"❌ 获取模型配置失败: {models_response.status_code}")
        return False

def test_frontend_accessibility():
    """测试前端页面可访问性"""
    print("\n🌐 测试前端页面可访问性...")
    
    try:
        # 测试主页
        main_response = requests.get('http://localhost:3000', timeout=5)
        print(f"✅ 主页访问: {main_response.status_code}")
        
        # 测试排班生成页面
        schedule_response = requests.get('http://localhost:3000/schedules/ai-generate', timeout=5)
        print(f"✅ 排班生成页面访问: {schedule_response.status_code}")
        
        # 测试模型配置页面
        models_response = requests.get('http://localhost:3000/models', timeout=5)
        print(f"✅ 模型配置页面访问: {models_response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"❌ 前端页面访问失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始排班生成功能测试")
    print("="*60)
    
    results = {}
    
    # 测试模型配置
    results['model_config'] = test_model_config()
    
    # 测试前端可访问性
    results['frontend'] = test_frontend_accessibility()
    
    # 测试对话模式API
    results['chat_mode'] = test_api_chat_mode()
    
    # 测试表格模式API
    results['table_mode'] = test_api_table_mode()
    
    # 总结测试结果
    print("\n" + "="*60)
    print("📊 测试结果总结")
    print("="*60)
    
    for test_name, success in results.items():
        status = "✅ 成功" if success else "❌ 失败"
        test_display = {
            'model_config': '模型配置',
            'frontend': '前端页面',
            'chat_mode': '对话模式API',
            'table_mode': '表格模式API'
        }
        print(f"{status} {test_display.get(test_name, test_name)}")
    
    success_count = sum(results.values())
    total_count = len(results)
    
    print(f"\n🎯 总体测试结果: {success_count}/{total_count} 通过")
    
    if success_count == total_count:
        print("🎉 所有功能测试通过！排班生成系统工作正常！")
    else:
        print("⚠️ 部分功能存在问题，请检查相关服务")
    
    return success_count == total_count

if __name__ == "__main__":
    main()
