#!/usr/bin/env python3
"""
Selenium自动化测试：排班生成页面
测试对话模式和表格模式的功能
"""
import time
import sys
import os
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import Select
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.options import Options

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def setup_driver():
    """设置Chrome驱动"""
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--window-size=1920,1080')
    # chrome_options.add_argument('--headless')  # 注释掉以便观察测试过程
    
    driver = webdriver.Chrome(options=chrome_options)
    driver.implicitly_wait(10)
    return driver

def login(driver):
    """登录系统"""
    print("🔐 开始登录...")
    driver.get("http://localhost:3000/login")
    
    # 等待登录页面加载
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.CSS_SELECTOR, "input[placeholder*='用户名']"))
    )
    
    # 输入用户名和密码
    username_input = driver.find_element(By.CSS_SELECTOR, "input[placeholder*='用户名']")
    password_input = driver.find_element(By.CSS_SELECTOR, "input[placeholder*='密码']")
    
    username_input.clear()
    username_input.send_keys("admin")
    
    password_input.clear()
    password_input.send_keys("admin123")
    
    # 点击登录按钮
    login_button = driver.find_element(By.CSS_SELECTOR, "button[type='submit']")
    login_button.click()
    
    # 等待登录成功，跳转到主页
    WebDriverWait(driver, 10).until(
        EC.url_contains("/dashboard")
    )
    print("✅ 登录成功")

def test_chat_mode(driver):
    """测试对话模式"""
    print("\n🤖 测试对话模式...")
    
    # 导航到排班生成页面
    driver.get("http://localhost:3000/schedules/ai-generate")
    
    # 等待页面加载
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.CSS_SELECTOR, ".ai-schedule-container"))
    )
    
    # 确保在对话模式
    chat_mode_button = driver.find_element(By.XPATH, "//button[contains(text(), '对话模式')]")
    if "primary" not in chat_mode_button.get_attribute("class"):
        chat_mode_button.click()
        time.sleep(1)
    
    print("✅ 切换到对话模式")
    
    # 配置基础信息
    try:
        # 选择科室
        department_select = Select(driver.find_element(By.CSS_SELECTOR, "select[placeholder*='科室']"))
        department_select.select_by_visible_text("西区")
        print("✅ 选择科室：西区")
    except:
        print("⚠️ 科室选择可能已有默认值")
    
    try:
        # 选择AI模型
        model_select = Select(driver.find_element(By.CSS_SELECTOR, "select[placeholder*='AI模型']"))
        model_select.select_by_index(0)  # 选择第一个可用模型
        print("✅ 选择AI模型")
    except:
        print("⚠️ AI模型选择可能已有默认值")
    
    # 输入排班表名称
    try:
        name_input = driver.find_element(By.CSS_SELECTOR, "input[placeholder*='排班表名称']")
        name_input.clear()
        name_input.send_keys("对话模式测试排班表")
        print("✅ 输入排班表名称")
    except:
        print("⚠️ 排班表名称输入可能已有默认值")
    
    # 设置日期范围
    try:
        date_inputs = driver.find_elements(By.CSS_SELECTOR, ".el-date-editor input")
        if len(date_inputs) >= 2:
            date_inputs[0].clear()
            date_inputs[0].send_keys("2025-07-28")
            date_inputs[1].clear()
            date_inputs[1].send_keys("2025-08-03")
            print("✅ 设置日期范围")
    except:
        print("⚠️ 日期设置可能已有默认值")
    
    # 在对话框中输入消息
    chat_input = WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.CSS_SELECTOR, ".chat-input-area textarea"))
    )
    
    test_message = """请为西区安排一周排班，具体要求如下：
1. 每天需要责任护士2名（◇责1、◇责2）
2. 每天需要值班护士1名
3. 每天需要治疗护士1名
4. 每人每周休息2天
5. 请用Markdown表格格式显示排班结果"""
    
    chat_input.clear()
    chat_input.send_keys(test_message)
    print("✅ 输入对话消息")
    
    # 点击发送按钮
    send_button = driver.find_element(By.XPATH, "//button[contains(text(), '发送')]")
    send_button.click()
    print("✅ 发送消息")
    
    # 等待AI回复
    print("⏳ 等待AI回复...")
    WebDriverWait(driver, 60).until(
        EC.presence_of_element_located((By.CSS_SELECTOR, ".ai-message .message-content"))
    )
    
    # 检查是否有AI回复
    ai_messages = driver.find_elements(By.CSS_SELECTOR, ".ai-message .message-content")
    if ai_messages:
        print(f"✅ 收到AI回复，共{len(ai_messages)}条消息")
        
        # 检查是否包含Markdown内容
        for i, message in enumerate(ai_messages):
            content = message.text
            if "表格" in content or "|" in content or "排班" in content:
                print(f"✅ 消息{i+1}包含排班相关内容")
            else:
                print(f"⚠️ 消息{i+1}内容：{content[:100]}...")
    else:
        print("❌ 未收到AI回复")
        return False
    
    # 测试多轮对话
    print("\n🔄 测试多轮对话...")
    follow_up_message = "请调整排班，让李海燕只上工作日管理岗位"
    
    chat_input.clear()
    chat_input.send_keys(follow_up_message)
    send_button.click()
    print("✅ 发送后续消息")
    
    # 等待第二轮回复
    time.sleep(5)  # 给AI一些时间回复
    
    ai_messages_after = driver.find_elements(By.CSS_SELECTOR, ".ai-message .message-content")
    if len(ai_messages_after) > len(ai_messages):
        print("✅ 多轮对话成功")
    else:
        print("⚠️ 多轮对话可能还在进行中")
    
    return True

def test_table_mode(driver):
    """测试表格模式"""
    print("\n📊 测试表格模式...")
    
    # 切换到表格模式
    table_mode_button = driver.find_element(By.XPATH, "//button[contains(text(), '表格模式')]")
    table_mode_button.click()
    time.sleep(2)
    print("✅ 切换到表格模式")
    
    # 输入排班要求
    requirements_textarea = WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.CSS_SELECTOR, ".quick-config textarea"))
    )
    
    requirements_text = """请为西区安排一周排班：
1. 每天需要责任护士2名
2. 每天需要值班护士1名
3. 每天需要治疗护士1名
4. 每人每周休息2天"""
    
    requirements_textarea.clear()
    requirements_textarea.send_keys(requirements_text)
    print("✅ 输入排班要求")
    
    # 点击生成按钮
    generate_button = driver.find_element(By.XPATH, "//button[contains(text(), '生成')]")
    generate_button.click()
    print("✅ 点击生成排班表")
    
    # 等待表格生成
    print("⏳ 等待表格生成...")
    try:
        WebDriverWait(driver, 60).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, ".schedule-table-container .el-table"))
        )
        print("✅ 排班表格生成成功")
        
        # 检查表格内容
        table_rows = driver.find_elements(By.CSS_SELECTOR, ".el-table tbody tr")
        if table_rows:
            print(f"✅ 表格包含{len(table_rows)}行数据")
            
            # 检查表格单元格
            cells_with_content = driver.find_elements(By.CSS_SELECTOR, ".schedule-cell:not(:empty)")
            print(f"✅ 表格包含{len(cells_with_content)}个有内容的单元格")
        else:
            print("⚠️ 表格无数据行")
            
    except:
        print("❌ 表格生成超时或失败")
        return False
    
    # 测试导出功能
    try:
        export_button = driver.find_element(By.XPATH, "//button[contains(text(), '导出表格')]")
        if export_button.is_enabled():
            print("✅ 导出按钮可用")
            # 注意：实际点击会下载文件，这里只检查按钮状态
        else:
            print("⚠️ 导出按钮不可用")
    except:
        print("⚠️ 未找到导出按钮")
    
    return True

def test_ui_elements(driver):
    """测试UI元素"""
    print("\n🎨 测试UI元素...")
    
    # 检查模式切换按钮
    mode_buttons = driver.find_elements(By.CSS_SELECTOR, ".el-button-group button")
    if len(mode_buttons) >= 2:
        print("✅ 模式切换按钮存在")
    else:
        print("⚠️ 模式切换按钮不完整")
    
    # 检查配置区域
    config_card = driver.find_elements(By.CSS_SELECTOR, ".config-card")
    if config_card:
        print("✅ 配置区域存在")
    else:
        print("⚠️ 配置区域不存在")
    
    # 检查对话区域
    chat_card = driver.find_elements(By.CSS_SELECTOR, ".chat-card")
    if chat_card:
        print("✅ 对话区域存在")
    else:
        print("⚠️ 对话区域不存在")
    
    # 检查表格区域
    table_card = driver.find_elements(By.CSS_SELECTOR, ".table-card")
    if table_card:
        print("✅ 表格区域存在")
    else:
        print("⚠️ 表格区域不存在")

def main():
    """主测试函数"""
    print("🚀 开始Selenium自动化测试：排班生成页面")
    print("="*60)
    
    driver = None
    try:
        # 设置驱动
        driver = setup_driver()
        print("✅ Chrome驱动设置成功")
        
        # 登录
        login(driver)
        
        # 测试UI元素
        test_ui_elements(driver)
        
        # 测试对话模式
        chat_success = test_chat_mode(driver)
        
        # 测试表格模式
        table_success = test_table_mode(driver)
        
        # 总结测试结果
        print("\n" + "="*60)
        print("📊 测试结果总结")
        print("="*60)
        print(f"✅ 登录测试: 成功")
        print(f"{'✅' if chat_success else '❌'} 对话模式测试: {'成功' if chat_success else '失败'}")
        print(f"{'✅' if table_success else '❌'} 表格模式测试: {'成功' if table_success else '失败'}")
        
        overall_success = chat_success and table_success
        print(f"\n🎯 总体测试结果: {'✅ 全部通过' if overall_success else '❌ 部分失败'}")
        
        if overall_success:
            print("🎉 排班生成页面功能测试完成，所有功能正常！")
        else:
            print("⚠️ 部分功能存在问题，请检查日志")
        
        # 保持浏览器打开一段时间以便观察
        print("\n⏳ 保持浏览器打开10秒以便观察...")
        time.sleep(10)
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        if driver:
            driver.quit()
            print("🔚 浏览器已关闭")

if __name__ == "__main__":
    main()
