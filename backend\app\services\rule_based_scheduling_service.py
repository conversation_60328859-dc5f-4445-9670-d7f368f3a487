from backend.app.models.models import db, Department, Position, Nurse, Schedule, ScheduleAssignment
from datetime import datetime, timedelta
import random

class RuleBasedSchedulingService:
    """
    基于规则的排班服务类
    实现传统的排班算法作为LLM的补充
    """
    
    def __init__(self):
        pass
    
    def generate_schedule(self, department_id, start_date, end_date, rules=None):
        """
        基于规则生成排班表
        :param department_id: 科室ID
        :param start_date: 开始日期
        :param end_date: 结束日期
        :param rules: 排班规则
        :return: 排班表数据
        """
        # 获取科室信息
        department = Department.query.get(department_id)
        if not department:
            raise ValueError("科室不存在")
        
        # 获取科室护士列表
        nurses = Nurse.query.filter_by(department_id=department_id, is_active=True).all()
        if not nurses:
            raise ValueError("该科室没有可用护士")
        
        # 获取所有岗位
        positions = Position.query.all()
        if not positions:
            raise ValueError("没有配置岗位信息")
        
        # 定义班次
        shifts = ['早班', '中班', '夜班']
        
        # 生成排班表
        schedule_data = []
        current_date = datetime.strptime(start_date, '%Y-%m-%d')
        end_date_obj = datetime.strptime(end_date, '%Y-%m-%d')
        
        # 为每个日期生成排班
        while current_date <= end_date_obj:
            date_str = current_date.strftime('%Y-%m-%d')
            
            # 为每个班次分配护士
            for shift in shifts:
                # 随机选择一个护士
                nurse = random.choice(nurses)
                
                # 随机选择一个岗位
                position = random.choice(positions)
                
                schedule_data.append({
                    'date': date_str,
                    'shift': shift,
                    'nurse_name': nurse.name,
                    'position': position.name
                })
            
            # 移动到下一天
            current_date += timedelta(days=1)
        
        return {
            'schedule': schedule_data
        }
    
    def generate_optimized_schedule(self, department_id, start_date, end_date, rules=None):
        """
        优化的基于规则的排班算法
        :param department_id: 科室ID
        :param start_date: 开始日期
        :param end_date: 结束日期
        :param rules: 排班规则
        :return: 排班表数据
        """
        # 获取科室信息
        department = Department.query.get(department_id)
        if not department:
            raise ValueError("科室不存在")
        
        # 获取科室护士列表
        nurses = Nurse.query.filter_by(department_id=department_id, is_active=True).all()
        if not nurses:
            raise ValueError("该科室没有可用护士")
        
        # 获取所有岗位
        positions = Position.query.all()
        if not positions:
            raise ValueError("没有配置岗位信息")
        
        # 定义班次
        shifts = ['早班', '中班', '夜班']
        
        # 生成排班表
        schedule_data = []
        current_date = datetime.strptime(start_date, '%Y-%m-%d')
        end_date_obj = datetime.strptime(end_date, '%Y-%m-%d')
        
        # 创建护士工作计数器
        nurse_work_count = {nurse.id: 0 for nurse in nurses}
        nurse_last_shift = {nurse.id: None for nurse in nurses}
        
        # 为每个日期生成排班
        while current_date <= end_date_obj:
            date_str = current_date.strftime('%Y-%m-%d')
            
            # 为每个班次分配护士
            for shift in shifts:
                # 选择最适合的护士
                selected_nurse = self._select_best_nurse(
                    nurses, nurse_work_count, nurse_last_shift, shift)
                
                # 随机选择一个岗位
                position = random.choice(positions)
                
                schedule_data.append({
                    'date': date_str,
                    'shift': shift,
                    'nurse_name': selected_nurse.name,
                    'position': position.name
                })
                
                # 更新护士工作计数器
                nurse_work_count[selected_nurse.id] += 1
                nurse_last_shift[selected_nurse.id] = shift
            
            # 移动到下一天
            current_date += timedelta(days=1)
        
        return {
            'schedule': schedule_data
        }
    
    def _select_best_nurse(self, nurses, nurse_work_count, nurse_last_shift, shift):
        """
        选择最适合的护士
        :param nurses: 护士列表
        :param nurse_work_count: 护士工作计数
        :param nurse_last_shift: 护士上一个班次
        :param shift: 当前班次
        :return: 选中的护士
        """
        # 优先选择工作量较少的护士
        min_work_count = min(nurse_work_count.values())
        candidate_nurses = [n for n in nurses if nurse_work_count[n.id] == min_work_count]
        
        # 避免连续同班次
        if len(candidate_nurses) > 1:
            no_repeat_nurses = [n for n in candidate_nurses if nurse_last_shift[n.id] != shift]
            if no_repeat_nurses:
                candidate_nurses = no_repeat_nurses
        
        # 随机选择一个护士
        return random.choice(candidate_nurses)