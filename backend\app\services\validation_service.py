from datetime import datetime
import re

class ValidationService:
    """
    数据验证服务类
    处理各种输入数据的验证
    """
    
    @staticmethod
    def validate_email(email):
        """验证邮箱格式"""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    @staticmethod
    def validate_employee_id(employee_id):
        """验证工号格式"""
        # 工号应为字母数字组合，长度3-20
        pattern = r'^[a-zA-Z0-9]{3,20}$'
        return re.match(pattern, employee_id) is not None
    
    @staticmethod
    def validate_date_format(date_str):
        """验证日期格式 YYYY-MM-DD"""
        try:
            datetime.strptime(date_str, '%Y-%m-%d')
            return True
        except ValueError:
            return False
    
    @staticmethod
    def validate_date_range(start_date, end_date):
        """验证日期范围"""
        try:
            start = datetime.strptime(start_date, '%Y-%m-%d')
            end = datetime.strptime(end_date, '%Y-%m-%d')
            return start <= end
        except ValueError:
            return False
    
    @staticmethod
    def validate_required_fields(data, required_fields):
        """验证必需字段"""
        missing_fields = []
        for field in required_fields:
            if field not in data or not data[field]:
                missing_fields.append(field)
        return missing_fields
    
    @staticmethod
    def validate_department_data(data):
        """验证科室数据"""
        errors = []
        
        # 验证必需字段
        required_fields = ['name']
        missing = ValidationService.validate_required_fields(data, required_fields)
        if missing:
            errors.append(f"缺少必需字段: {', '.join(missing)}")
        
        # 验证科室名称长度
        if 'name' in data and len(data['name']) > 100:
            errors.append("科室名称长度不能超过100个字符")
        
        return errors
    
    @staticmethod
    def validate_nurse_data(data):
        """验证护士数据"""
        errors = []
        
        # 验证必需字段
        required_fields = ['name', 'employee_id', 'department_id']
        missing = ValidationService.validate_required_fields(data, required_fields)
        if missing:
            errors.append(f"缺少必需字段: {', '.join(missing)}")
        
        # 验证工号格式
        if 'employee_id' in data and not ValidationService.validate_employee_id(data['employee_id']):
            errors.append("工号格式不正确，应为3-20位字母数字组合")
        
        # 验证姓名长度
        if 'name' in data and len(data['name']) > 100:
            errors.append("姓名长度不能超过100个字符")
        
        return errors
    
    @staticmethod
    def validate_schedule_data(data):
        """验证排班表数据"""
        errors = []
        
        # 验证必需字段
        required_fields = ['name', 'department_id', 'start_date', 'end_date']
        missing = ValidationService.validate_required_fields(data, required_fields)
        if missing:
            errors.append(f"缺少必需字段: {', '.join(missing)}")
        
        # 验证日期格式
        if 'start_date' in data and not ValidationService.validate_date_format(data['start_date']):
            errors.append("开始日期格式不正确，应为YYYY-MM-DD")
        
        if 'end_date' in data and not ValidationService.validate_date_format(data['end_date']):
            errors.append("结束日期格式不正确，应为YYYY-MM-DD")
        
        # 验证日期范围
        if 'start_date' in data and 'end_date' in data:
            if not ValidationService.validate_date_range(data['start_date'], data['end_date']):
                errors.append("开始日期不能晚于结束日期")
        
        # 验证排班表名称长度
        if 'name' in data and len(data['name']) > 100:
            errors.append("排班表名称长度不能超过100个字符")
        
        return errors