from backend.app.models.models import db, Department, Position, Nurse, Schedule, ScheduleAssignment
from backend.app.services.llm_service import LLMService
from datetime import datetime, timedelta

class SchedulingService:
    """
    排班服务类
    处理排班相关的核心业务逻辑
    """
    
    def __init__(self):
        pass
    
    def generate_schedule_with_llm(self, department_id, requirements, llm_config_id=None, start_date=None, end_date=None):
        """
        使用LLM生成排班表
        :param department_id: 科室ID
        :param requirements: 排班需求描述
        :param llm_config_id: LLM配置ID
        :param start_date: 开始日期
        :param end_date: 结束日期
        :return: 生成的排班表
        """
        # 获取科室信息
        department = Department.query.get(department_id)
        if not department:
            raise ValueError("科室不存在")
        
        # 获取科室护士列表
        nurses = Nurse.query.filter_by(department_id=department_id, is_active=True).all()
        
        # 获取所有岗位
        positions = Position.query.all()
        
        # 调用LLM服务生成排班表
        llm_service = LLMService(llm_config_id)
        schedule_data = llm_service.generate_schedule(requirements, nurses, positions, department, start_date, end_date)
        
        return schedule_data
    
    def create_schedule_from_data(self, department_id, schedule_name, start_date, end_date, schedule_data):
        """
        根据数据创建排班表
        :param department_id: 科室ID
        :param schedule_name: 排班表名称
        :param start_date: 开始日期
        :param end_date: 结束日期
        :param schedule_data: 排班数据
        :return: 排班表对象
        """
        # 创建排班表
        schedule = Schedule(
            name=schedule_name,
            department_id=department_id,
            start_date=start_date,
            end_date=end_date
        )
        db.session.add(schedule)
        db.session.flush()
        
        # 处理排班分配数据
        if 'schedule' in schedule_data:
            assignments = schedule_data['schedule']
            for assignment_data in assignments:
                # 查找护士
                nurse = Nurse.query.filter_by(name=assignment_data['nurse_name']).first()
                if not nurse:
                    continue
                
                # 查找岗位
                position = Position.query.filter_by(name=assignment_data['position']).first()
                if not position:
                    continue
                
                # 创建排班分配
                assignment = ScheduleAssignment(
                    schedule_id=schedule.id,
                    nurse_id=nurse.id,
                    date=assignment_data['date'],
                    position_id=position.id,
                    shift=assignment_data.get('shift', '未知班次')
                )
                db.session.add(assignment)
        
        db.session.commit()
        return schedule
    
    def get_schedule_statistics(self, schedule_id):
        """
        获取排班表统计信息
        :param schedule_id: 排班表ID
        :return: 统计信息
        """
        schedule = Schedule.query.get(schedule_id)
        if not schedule:
            raise ValueError("排班表不存在")
        
        # 获取排班分配
        assignments = ScheduleAssignment.query.filter_by(schedule_id=schedule_id).all()
        
        # 统计每位护士的工作情况
        nurse_stats = {}
        for assignment in assignments:
            nurse_id = assignment.nurse_id
            if nurse_id not in nurse_stats:
                nurse_stats[nurse_id] = {
                    'nurse_name': assignment.nurse.name,
                    'total_shifts': 0,
                    'shift_breakdown': {}
                }
            
            nurse_stats[nurse_id]['total_shifts'] += 1
            shift = assignment.shift or '未知班次'
            if shift not in nurse_stats[nurse_id]['shift_breakdown']:
                nurse_stats[nurse_id]['shift_breakdown'][shift] = 0
            nurse_stats[nurse_id]['shift_breakdown'][shift] += 1
        
        return {
            'schedule_name': schedule.name,
            'department_name': schedule.department.name,
            'total_days': (schedule.end_date - schedule.start_date).days + 1,
            'nurse_workload': list(nurse_stats.values())
        }
    
    def get_nurse_position_compatibility(self, nurse_id):
        """
        获取护士岗位胜任能力
        :param nurse_id: 护士ID
        :return: 岗位胜任能力信息
        """
        nurse = Nurse.query.get(nurse_id)
        if not nurse:
            raise ValueError("护士不存在")
        
        # 获取所有岗位
        all_positions = Position.query.all()
        
        # 获取护士当前胜任的岗位
        nurse_positions = set(p.id for p in nurse.positions)
        
        compatibility = []
        for position in all_positions:
            compatibility.append({
                'position_id': position.id,
                'position_name': position.name,
                'is_compatible': position.id in nurse_positions
            })
        
        return {
            'nurse_name': nurse.name,
            'compatibility': compatibility
        }
    
    def update_nurse_positions(self, nurse_id, position_ids):
        """
        更新护士胜任岗位
        :param nurse_id: 护士ID
        :param position_ids: 岗位ID列表
        :return: 更新后的护士信息
        """
        nurse = Nurse.query.get(nurse_id)
        if not nurse:
            raise ValueError("护士不存在")
        
        # 获取岗位
        positions = Position.query.filter(Position.id.in_(position_ids)).all()
        nurse.positions = positions
        
        db.session.commit()
        return nurse.to_dict()