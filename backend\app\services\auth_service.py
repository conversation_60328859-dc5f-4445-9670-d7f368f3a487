from backend.app.models.models import db, User
import jwt
from datetime import datetime, timedelta
from flask import current_app

class AuthService:
    """
    认证服务类
    处理用户认证和权限验证
    """
    
    @staticmethod
    def authenticate_user(username, password):
        """
        验证用户凭据
        :param username: 用户名
        :param password: 密码
        :return: 用户对象和token，如果验证失败返回None
        """
        user = User.query.filter_by(username=username, is_active=True).first()
        if user and user.check_password(password):
            # 生成JWT token
            token = AuthService.generate_token(user.id)
            return user, token
        return None, None
    
    @staticmethod
    def generate_token(user_id):
        """
        生成JWT token
        :param user_id: 用户ID
        :return: JWT token
        """
        payload = {
            'user_id': user_id,
            'exp': datetime.utcnow() + timedelta(hours=24),
            'iat': datetime.utcnow()
        }
        # 使用简单的密钥，实际应用中应该从配置中读取
        secret_key = 'nurse_schedule_secret_key'
        token = jwt.encode(payload, secret_key, algorithm='HS256')
        return token
    
    @staticmethod
    def verify_token(token):
        """
        验证JWT token
        :param token: JWT token
        :return: 用户ID，如果验证失败返回None
        """
        try:
            secret_key = 'nurse_schedule_secret_key'
            payload = jwt.decode(token, secret_key, algorithms=['HS256'])
            return payload['user_id']
        except jwt.ExpiredSignatureError:
            return None
        except jwt.InvalidTokenError:
            return None
    
    @staticmethod
    def create_user(username, email, password, role='user', department_id=None):
        """
        创建新用户
        :param username: 用户名
        :param email: 邮箱
        :param password: 密码
        :param role: 角色
        :param department_id: 科室ID
        :return: 用户对象
        """
        user = User(
            username=username,
            email=email,
            role=role,
            department_id=department_id
        )
        user.set_password(password)
        db.session.add(user)
        db.session.commit()
        return user