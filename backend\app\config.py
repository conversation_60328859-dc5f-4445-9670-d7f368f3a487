"""
应用配置文件
"""
import os
from typing import Dict, Any

class Config:
    """基础配置类"""
    
    # 数据库配置
    SQLALCHEMY_DATABASE_URI = 'sqlite:///nurse_schedule.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'connect_args': {'check_same_thread': False},
        'pool_pre_ping': True
    }
    
    # JSON配置
    JSON_AS_ASCII = False
    
    # JWT配置
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY', 'your-secret-key-here')
    JWT_ACCESS_TOKEN_EXPIRES = 24 * 60 * 60  # 24小时
    
    # CORS配置
    CORS_ORIGINS = ['http://localhost:3000', 'http://localhost:3001']

class ModelConfig:
    """AI模型配置类"""
    
    # 默认模型配置
    DEFAULT_MODELS = {
        'openrouter': {
            'name': '<PERSON><PERSON> K2 (OpenRouter)',
            'provider': 'openrouter',
            'api_key': '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',
            'base_url': 'https://openrouter.ai/api/v1',
            'model': 'moonshotai/kimi-k2:free',
            'max_tokens': 16000,
            'temperature': 0.7,
            'enabled': True,
            'description': 'Moonshot AI的Kimi K2模型，免费版本，适合日常排班任务'
        },
        'siliconflow': {
            'name': 'Qwen 3(硅基流动)',
            'provider': 'siliconflow',
            'api_key': 'sk-siiswpsbpksaraejiwejzaiejlbfrjdysshlnlhpvpqzfruu',
            'base_url': 'https://api.siliconflow.cn/v1',
            'model': 'Qwen/Qwen3-235B-A22B-Instruct-2507',
            'max_tokens': 16000,
            'temperature': 0.6,
            'enabled': True,
            'description': '硅基流动平台的Qwen 3模型，中文理解能力强，适合中文排班场景'
        }
    }
    
    @classmethod
    def get_model_config(cls, model_id: str) -> Dict[str, Any]:
        """获取指定模型的配置"""
        return cls.DEFAULT_MODELS.get(model_id, cls.DEFAULT_MODELS['openai'])
    
    @classmethod
    def get_all_models(cls) -> Dict[str, Dict[str, Any]]:
        """获取所有可用模型"""
        return cls.DEFAULT_MODELS
    
    @classmethod
    def get_enabled_models(cls) -> Dict[str, Dict[str, Any]]:
        """获取所有启用的模型"""
        return {k: v for k, v in cls.DEFAULT_MODELS.items() if v.get('enabled', False)}

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    FLASK_ENV = 'development'

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    FLASK_ENV = 'production'

class TestingConfig(Config):
    """测试环境配置"""
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'

# 配置映射
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
