"""
模型配置服务
"""
from typing import Dict, List, Optional, Any
from backend.app.models.models import db, ModelConfig
from backend.app.config import ModelConfig as DefaultModelConfig

class ModelConfigService:
    """模型配置服务类"""
    
    @staticmethod
    def init_default_models():
        """初始化默认模型配置"""
        try:
            # 检查是否已有模型配置
            existing_count = ModelConfig.query.count()
            if existing_count > 0:
                print(f"已存在 {existing_count} 个模型配置，跳过初始化")
                return
            
            # 添加默认模型配置
            default_models = DefaultModelConfig.get_all_models()
            
            for model_id, config in default_models.items():
                # 检查模型是否已存在
                existing_model = ModelConfig.query.filter_by(model_id=model_id).first()
                if existing_model:
                    continue
                
                model_config = ModelConfig(
                    model_id=model_id,
                    name=config['name'],
                    provider=config['provider'],
                    api_key=config['api_key'],
                    base_url=config['base_url'],
                    model=config['model'],
                    max_tokens=config.get('max_tokens', 4000),
                    temperature=config.get('temperature', 0.7),
                    enabled=config.get('enabled', True),
                    is_default=(model_id == 'openai'),  # OpenAI作为默认模型
                    description=config.get('description', '')
                )
                
                db.session.add(model_config)
            
            db.session.commit()
            print("默认模型配置初始化完成")
            
        except Exception as e:
            db.session.rollback()
            print(f"初始化默认模型配置失败: {str(e)}")
            raise
    
    @staticmethod
    def get_all_models() -> List[Dict[str, Any]]:
        """获取所有模型配置"""
        try:
            models = ModelConfig.query.order_by(ModelConfig.is_default.desc(), ModelConfig.created_at).all()
            return [model.to_dict() for model in models]
        except Exception as e:
            print(f"获取模型配置失败: {str(e)}")
            return []
    
    @staticmethod
    def get_enabled_models() -> List[Dict[str, Any]]:
        """获取所有启用的模型配置"""
        try:
            models = ModelConfig.query.filter_by(enabled=True).order_by(
                ModelConfig.is_default.desc(), ModelConfig.created_at
            ).all()
            return [model.to_dict() for model in models]
        except Exception as e:
            print(f"获取启用模型配置失败: {str(e)}")
            return []
    
    @staticmethod
    def get_default_model() -> Optional[Dict[str, Any]]:
        """获取默认模型配置"""
        try:
            model = ModelConfig.query.filter_by(is_default=True, enabled=True).first()
            if model:
                return model.to_dict_full()
            
            # 如果没有默认模型，返回第一个启用的模型
            model = ModelConfig.query.filter_by(enabled=True).first()
            if model:
                return model.to_dict_full()
            
            return None
        except Exception as e:
            print(f"获取默认模型配置失败: {str(e)}")
            return None
    
    @staticmethod
    def get_model_by_id(model_id: str) -> Optional[Dict[str, Any]]:
        """根据模型ID获取模型配置"""
        try:
            model = ModelConfig.query.filter_by(model_id=model_id, enabled=True).first()
            if model:
                return model.to_dict_full()
            return None
        except Exception as e:
            print(f"获取模型配置失败: {str(e)}")
            return None
    
    @staticmethod
    def create_model(data: Dict[str, Any]) -> Dict[str, Any]:
        """创建新的模型配置"""
        try:
            # 检查模型ID是否已存在
            existing_model = ModelConfig.query.filter_by(model_id=data['model_id']).first()
            if existing_model:
                raise ValueError(f"模型ID '{data['model_id']}' 已存在")
            
            # 如果设置为默认模型，先取消其他模型的默认状态
            if data.get('is_default', False):
                ModelConfig.query.filter_by(is_default=True).update({'is_default': False})
            
            model_config = ModelConfig(
                model_id=data['model_id'],
                name=data['name'],
                provider=data['provider'],
                api_key=data['api_key'],
                base_url=data['base_url'],
                model=data['model'],
                max_tokens=data.get('max_tokens', 4000),
                temperature=data.get('temperature', 0.7),
                enabled=data.get('enabled', True),
                is_default=data.get('is_default', False),
                description=data.get('description', '')
            )
            
            db.session.add(model_config)
            db.session.commit()
            
            return model_config.to_dict()
            
        except Exception as e:
            db.session.rollback()
            print(f"创建模型配置失败: {str(e)}")
            raise
    
    @staticmethod
    def update_model(model_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """更新模型配置"""
        try:
            model = ModelConfig.query.filter_by(model_id=model_id).first()
            if not model:
                raise ValueError(f"模型 '{model_id}' 不存在")
            
            # 如果设置为默认模型，先取消其他模型的默认状态
            if data.get('is_default', False) and not model.is_default:
                ModelConfig.query.filter_by(is_default=True).update({'is_default': False})
            
            # 更新模型配置
            for key, value in data.items():
                if hasattr(model, key) and key != 'model_id':  # 不允许修改model_id
                    setattr(model, key, value)
            
            db.session.commit()
            return model.to_dict()
            
        except Exception as e:
            db.session.rollback()
            print(f"更新模型配置失败: {str(e)}")
            raise
    
    @staticmethod
    def delete_model(model_id: str) -> bool:
        """删除模型配置"""
        try:
            model = ModelConfig.query.filter_by(model_id=model_id).first()
            if not model:
                raise ValueError(f"模型 '{model_id}' 不存在")
            
            # 如果删除的是默认模型，需要设置新的默认模型
            if model.is_default:
                # 找到下一个启用的模型设为默认
                next_model = ModelConfig.query.filter(
                    ModelConfig.model_id != model_id,
                    ModelConfig.enabled == True
                ).first()
                if next_model:
                    next_model.is_default = True
            
            db.session.delete(model)
            db.session.commit()
            return True
            
        except Exception as e:
            db.session.rollback()
            print(f"删除模型配置失败: {str(e)}")
            raise
    
    @staticmethod
    def set_default_model(model_id: str) -> Dict[str, Any]:
        """设置默认模型"""
        try:
            # 取消所有模型的默认状态
            ModelConfig.query.filter_by(is_default=True).update({'is_default': False})
            
            # 设置新的默认模型
            model = ModelConfig.query.filter_by(model_id=model_id).first()
            if not model:
                raise ValueError(f"模型 '{model_id}' 不存在")
            
            model.is_default = True
            model.enabled = True  # 确保默认模型是启用的
            
            db.session.commit()
            return model.to_dict()
            
        except Exception as e:
            db.session.rollback()
            print(f"设置默认模型失败: {str(e)}")
            raise
