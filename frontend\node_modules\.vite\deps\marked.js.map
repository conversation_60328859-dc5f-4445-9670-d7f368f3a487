{"version": 3, "sources": ["../../marked/src/defaults.ts", "../../marked/src/rules.ts", "../../marked/src/helpers.ts", "../../marked/src/Tokenizer.ts", "../../marked/src/Lexer.ts", "../../marked/src/Renderer.ts", "../../marked/src/TextRenderer.ts", "../../marked/src/Parser.ts", "../../marked/src/Hooks.ts", "../../marked/src/Instance.ts", "../../marked/src/marked.ts"], "sourcesContent": ["import type { MarkedOptions } from './MarkedOptions.ts';\n\n/**\n * Gets the original marked default options.\n */\nexport function _getDefaults<ParserOutput = string, RendererOutput = string>(): MarkedOptions<ParserOutput, RendererOutput> {\n  return {\n    async: false,\n    breaks: false,\n    extensions: null,\n    gfm: true,\n    hooks: null,\n    pedantic: false,\n    renderer: null,\n    silent: false,\n    tokenizer: null,\n    walkTokens: null,\n  };\n}\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport let _defaults: MarkedOptions<any, any> = _getDefaults();\n\nexport function changeDefaults<ParserOutput = string, RendererOutput = string>(newDefaults: MarkedOptions<ParserOutput, RendererOutput>) {\n  _defaults = newDefaults;\n}\n", "const noopTest = { exec: () => null } as unknown as RegExp;\n\nfunction edit(regex: string | RegExp, opt = '') {\n  let source = typeof regex === 'string' ? regex : regex.source;\n  const obj = {\n    replace: (name: string | RegExp, val: string | RegExp) => {\n      let valSource = typeof val === 'string' ? val : val.source;\n      valSource = valSource.replace(other.caret, '$1');\n      source = source.replace(name, valSource);\n      return obj;\n    },\n    getRegex: () => {\n      return new RegExp(source, opt);\n    },\n  };\n  return obj;\n}\n\nexport const other = {\n  codeRemoveIndent: /^(?: {1,4}| {0,3}\\t)/gm,\n  outputLinkReplace: /\\\\([\\[\\]])/g,\n  indentCodeCompensation: /^(\\s+)(?:```)/,\n  beginningSpace: /^\\s+/,\n  endingHash: /#$/,\n  startingSpaceChar: /^ /,\n  endingSpaceChar: / $/,\n  nonSpaceChar: /[^ ]/,\n  newLineCharGlobal: /\\n/g,\n  tabCharGlobal: /\\t/g,\n  multipleSpaceGlobal: /\\s+/g,\n  blankLine: /^[ \\t]*$/,\n  doubleBlankLine: /\\n[ \\t]*\\n[ \\t]*$/,\n  blockquoteStart: /^ {0,3}>/,\n  blockquoteSetextReplace: /\\n {0,3}((?:=+|-+) *)(?=\\n|$)/g,\n  blockquoteSetextReplace2: /^ {0,3}>[ \\t]?/gm,\n  listReplaceTabs: /^\\t+/,\n  listReplaceNesting: /^ {1,4}(?=( {4})*[^ ])/g,\n  listIsTask: /^\\[[ xX]\\] /,\n  listReplaceTask: /^\\[[ xX]\\] +/,\n  anyLine: /\\n.*\\n/,\n  hrefBrackets: /^<(.*)>$/,\n  tableDelimiter: /[:|]/,\n  tableAlignChars: /^\\||\\| *$/g,\n  tableRowBlankLine: /\\n[ \\t]*$/,\n  tableAlignRight: /^ *-+: *$/,\n  tableAlignCenter: /^ *:-+: *$/,\n  tableAlignLeft: /^ *:-+ *$/,\n  startATag: /^<a /i,\n  endATag: /^<\\/a>/i,\n  startPreScriptTag: /^<(pre|code|kbd|script)(\\s|>)/i,\n  endPreScriptTag: /^<\\/(pre|code|kbd|script)(\\s|>)/i,\n  startAngleBracket: /^</,\n  endAngleBracket: />$/,\n  pedanticHrefTitle: /^([^'\"]*[^\\s])\\s+(['\"])(.*)\\2/,\n  unicodeAlphaNumeric: /[\\p{L}\\p{N}]/u,\n  escapeTest: /[&<>\"']/,\n  escapeReplace: /[&<>\"']/g,\n  escapeTestNoEncode: /[<>\"']|&(?!(#\\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\\w+);)/,\n  escapeReplaceNoEncode: /[<>\"']|&(?!(#\\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\\w+);)/g,\n  unescapeTest: /&(#(?:\\d+)|(?:#x[0-9A-Fa-f]+)|(?:\\w+));?/ig,\n  caret: /(^|[^\\[])\\^/g,\n  percentDecode: /%25/g,\n  findPipe: /\\|/g,\n  splitPipe: / \\|/,\n  slashPipe: /\\\\\\|/g,\n  carriageReturn: /\\r\\n|\\r/g,\n  spaceLine: /^ +$/gm,\n  notSpaceStart: /^\\S*/,\n  endingNewline: /\\n$/,\n  listItemRegex: (bull: string) => new RegExp(`^( {0,3}${bull})((?:[\\t ][^\\\\n]*)?(?:\\\\n|$))`),\n  nextBulletRegex: (indent: number) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}(?:[*+-]|\\\\d{1,9}[.)])((?:[ \\t][^\\\\n]*)?(?:\\\\n|$))`),\n  hrRegex: (indent: number) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\\\* *){3,})(?:\\\\n+|$)`),\n  fencesBeginRegex: (indent: number) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}(?:\\`\\`\\`|~~~)`),\n  headingBeginRegex: (indent: number) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}#`),\n  htmlBeginRegex: (indent: number) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}<(?:[a-z].*>|!--)`, 'i'),\n};\n\n/**\n * Block-Level Grammar\n */\n\nconst newline = /^(?:[ \\t]*(?:\\n|$))+/;\nconst blockCode = /^((?: {4}| {0,3}\\t)[^\\n]+(?:\\n(?:[ \\t]*(?:\\n|$))*)?)+/;\nconst fences = /^ {0,3}(`{3,}(?=[^`\\n]*(?:\\n|$))|~{3,})([^\\n]*)(?:\\n|$)(?:|([\\s\\S]*?)(?:\\n|$))(?: {0,3}\\1[~`]* *(?=\\n|$)|$)/;\nconst hr = /^ {0,3}((?:-[\\t ]*){3,}|(?:_[ \\t]*){3,}|(?:\\*[ \\t]*){3,})(?:\\n+|$)/;\nconst heading = /^ {0,3}(#{1,6})(?=\\s|$)(.*)(?:\\n+|$)/;\nconst bullet = /(?:[*+-]|\\d{1,9}[.)])/;\nconst lheadingCore = /^(?!bull |blockCode|fences|blockquote|heading|html|table)((?:.|\\n(?!\\s*?\\n|bull |blockCode|fences|blockquote|heading|html|table))+?)\\n {0,3}(=+|-+) *(?:\\n+|$)/;\nconst lheading = edit(lheadingCore)\n  .replace(/bull/g, bullet) // lists can interrupt\n  .replace(/blockCode/g, /(?: {4}| {0,3}\\t)/) // indented code blocks can interrupt\n  .replace(/fences/g, / {0,3}(?:`{3,}|~{3,})/) // fenced code blocks can interrupt\n  .replace(/blockquote/g, / {0,3}>/) // blockquote can interrupt\n  .replace(/heading/g, / {0,3}#{1,6}/) // ATX heading can interrupt\n  .replace(/html/g, / {0,3}<[^\\n>]+>\\n/) // block html can interrupt\n  .replace(/\\|table/g, '') // table not in commonmark\n  .getRegex();\nconst lheadingGfm = edit(lheadingCore)\n  .replace(/bull/g, bullet) // lists can interrupt\n  .replace(/blockCode/g, /(?: {4}| {0,3}\\t)/) // indented code blocks can interrupt\n  .replace(/fences/g, / {0,3}(?:`{3,}|~{3,})/) // fenced code blocks can interrupt\n  .replace(/blockquote/g, / {0,3}>/) // blockquote can interrupt\n  .replace(/heading/g, / {0,3}#{1,6}/) // ATX heading can interrupt\n  .replace(/html/g, / {0,3}<[^\\n>]+>\\n/) // block html can interrupt\n  .replace(/table/g, / {0,3}\\|?(?:[:\\- ]*\\|)+[\\:\\- ]*\\n/) // table can interrupt\n  .getRegex();\nconst _paragraph = /^([^\\n]+(?:\\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\\n)[^\\n]+)*)/;\nconst blockText = /^[^\\n]+/;\nconst _blockLabel = /(?!\\s*\\])(?:\\\\.|[^\\[\\]\\\\])+/;\nconst def = edit(/^ {0,3}\\[(label)\\]: *(?:\\n[ \\t]*)?([^<\\s][^\\s]*|<.*?>)(?:(?: +(?:\\n[ \\t]*)?| *\\n[ \\t]*)(title))? *(?:\\n+|$)/)\n  .replace('label', _blockLabel)\n  .replace('title', /(?:\"(?:\\\\\"?|[^\"\\\\])*\"|'[^'\\n]*(?:\\n[^'\\n]+)*\\n?'|\\([^()]*\\))/)\n  .getRegex();\n\nconst list = edit(/^( {0,3}bull)([ \\t][^\\n]+?)?(?:\\n|$)/)\n  .replace(/bull/g, bullet)\n  .getRegex();\n\nconst _tag = 'address|article|aside|base|basefont|blockquote|body|caption'\n  + '|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption'\n  + '|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe'\n  + '|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option'\n  + '|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title'\n  + '|tr|track|ul';\nconst _comment = /<!--(?:-?>|[\\s\\S]*?(?:-->|$))/;\nconst html = edit(\n  '^ {0,3}(?:' // optional indentation\n+ '<(script|pre|style|textarea)[\\\\s>][\\\\s\\\\S]*?(?:</\\\\1>[^\\\\n]*\\\\n+|$)' // (1)\n+ '|comment[^\\\\n]*(\\\\n+|$)' // (2)\n+ '|<\\\\?[\\\\s\\\\S]*?(?:\\\\?>\\\\n*|$)' // (3)\n+ '|<![A-Z][\\\\s\\\\S]*?(?:>\\\\n*|$)' // (4)\n+ '|<!\\\\[CDATA\\\\[[\\\\s\\\\S]*?(?:\\\\]\\\\]>\\\\n*|$)' // (5)\n+ '|</?(tag)(?: +|\\\\n|/?>)[\\\\s\\\\S]*?(?:(?:\\\\n[ \\t]*)+\\\\n|$)' // (6)\n+ '|<(?!script|pre|style|textarea)([a-z][\\\\w-]*)(?:attribute)*? */?>(?=[ \\\\t]*(?:\\\\n|$))[\\\\s\\\\S]*?(?:(?:\\\\n[ \\t]*)+\\\\n|$)' // (7) open tag\n+ '|</(?!script|pre|style|textarea)[a-z][\\\\w-]*\\\\s*>(?=[ \\\\t]*(?:\\\\n|$))[\\\\s\\\\S]*?(?:(?:\\\\n[ \\t]*)+\\\\n|$)' // (7) closing tag\n+ ')', 'i')\n  .replace('comment', _comment)\n  .replace('tag', _tag)\n  .replace('attribute', / +[a-zA-Z:_][\\w.:-]*(?: *= *\"[^\"\\n]*\"| *= *'[^'\\n]*'| *= *[^\\s\"'=<>`]+)?/)\n  .getRegex();\n\nconst paragraph = edit(_paragraph)\n  .replace('hr', hr)\n  .replace('heading', ' {0,3}#{1,6}(?:\\\\s|$)')\n  .replace('|lheading', '') // setext headings don't interrupt commonmark paragraphs\n  .replace('|table', '')\n  .replace('blockquote', ' {0,3}>')\n  .replace('fences', ' {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n')\n  .replace('list', ' {0,3}(?:[*+-]|1[.)]) ') // only lists starting from 1 can interrupt\n  .replace('html', '</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)')\n  .replace('tag', _tag) // pars can be interrupted by type (6) html blocks\n  .getRegex();\n\nconst blockquote = edit(/^( {0,3}> ?(paragraph|[^\\n]*)(?:\\n|$))+/)\n  .replace('paragraph', paragraph)\n  .getRegex();\n\n/**\n * Normal Block Grammar\n */\n\nconst blockNormal = {\n  blockquote,\n  code: blockCode,\n  def,\n  fences,\n  heading,\n  hr,\n  html,\n  lheading,\n  list,\n  newline,\n  paragraph,\n  table: noopTest,\n  text: blockText,\n};\n\ntype BlockKeys = keyof typeof blockNormal;\n\n/**\n * GFM Block Grammar\n */\n\nconst gfmTable = edit(\n  '^ *([^\\\\n ].*)\\\\n' // Header\n+ ' {0,3}((?:\\\\| *)?:?-+:? *(?:\\\\| *:?-+:? *)*(?:\\\\| *)?)' // Align\n+ '(?:\\\\n((?:(?! *\\\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\\\n|$))*)\\\\n*|$)') // Cells\n  .replace('hr', hr)\n  .replace('heading', ' {0,3}#{1,6}(?:\\\\s|$)')\n  .replace('blockquote', ' {0,3}>')\n  .replace('code', '(?: {4}| {0,3}\\t)[^\\\\n]')\n  .replace('fences', ' {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n')\n  .replace('list', ' {0,3}(?:[*+-]|1[.)]) ') // only lists starting from 1 can interrupt\n  .replace('html', '</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)')\n  .replace('tag', _tag) // tables can be interrupted by type (6) html blocks\n  .getRegex();\n\nconst blockGfm: Record<BlockKeys, RegExp> = {\n  ...blockNormal,\n  lheading: lheadingGfm,\n  table: gfmTable,\n  paragraph: edit(_paragraph)\n    .replace('hr', hr)\n    .replace('heading', ' {0,3}#{1,6}(?:\\\\s|$)')\n    .replace('|lheading', '') // setext headings don't interrupt commonmark paragraphs\n    .replace('table', gfmTable) // interrupt paragraphs with table\n    .replace('blockquote', ' {0,3}>')\n    .replace('fences', ' {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n')\n    .replace('list', ' {0,3}(?:[*+-]|1[.)]) ') // only lists starting from 1 can interrupt\n    .replace('html', '</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)')\n    .replace('tag', _tag) // pars can be interrupted by type (6) html blocks\n    .getRegex(),\n};\n\n/**\n * Pedantic grammar (original John Gruber's loose markdown specification)\n */\n\nconst blockPedantic: Record<BlockKeys, RegExp> = {\n  ...blockNormal,\n  html: edit(\n    '^ *(?:comment *(?:\\\\n|\\\\s*$)'\n    + '|<(tag)[\\\\s\\\\S]+?</\\\\1> *(?:\\\\n{2,}|\\\\s*$)' // closed tag\n    + '|<tag(?:\"[^\"]*\"|\\'[^\\']*\\'|\\\\s[^\\'\"/>\\\\s]*)*?/?> *(?:\\\\n{2,}|\\\\s*$))')\n    .replace('comment', _comment)\n    .replace(/tag/g, '(?!(?:'\n      + 'a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub'\n      + '|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)'\n      + '\\\\b)\\\\w+(?!:|[^\\\\w\\\\s@]*@)\\\\b')\n    .getRegex(),\n  def: /^ *\\[([^\\]]+)\\]: *<?([^\\s>]+)>?(?: +([\"(][^\\n]+[\")]))? *(?:\\n+|$)/,\n  heading: /^(#{1,6})(.*)(?:\\n+|$)/,\n  fences: noopTest, // fences not supported\n  lheading: /^(.+?)\\n {0,3}(=+|-+) *(?:\\n+|$)/,\n  paragraph: edit(_paragraph)\n    .replace('hr', hr)\n    .replace('heading', ' *#{1,6} *[^\\n]')\n    .replace('lheading', lheading)\n    .replace('|table', '')\n    .replace('blockquote', ' {0,3}>')\n    .replace('|fences', '')\n    .replace('|list', '')\n    .replace('|html', '')\n    .replace('|tag', '')\n    .getRegex(),\n};\n\n/**\n * Inline-Level Grammar\n */\n\nconst escape = /^\\\\([!\"#$%&'()*+,\\-./:;<=>?@\\[\\]\\\\^_`{|}~])/;\nconst inlineCode = /^(`+)([^`]|[^`][\\s\\S]*?[^`])\\1(?!`)/;\nconst br = /^( {2,}|\\\\)\\n(?!\\s*$)/;\nconst inlineText = /^(`+|[^`])(?:(?= {2,}\\n)|[\\s\\S]*?(?:(?=[\\\\<!\\[`*_]|\\b_|$)|[^ ](?= {2,}\\n)))/;\n\n// list of unicode punctuation marks, plus any missing characters from CommonMark spec\nconst _punctuation = /[\\p{P}\\p{S}]/u;\nconst _punctuationOrSpace = /[\\s\\p{P}\\p{S}]/u;\nconst _notPunctuationOrSpace = /[^\\s\\p{P}\\p{S}]/u;\nconst punctuation = edit(/^((?![*_])punctSpace)/, 'u')\n  .replace(/punctSpace/g, _punctuationOrSpace).getRegex();\n\n// GFM allows ~ inside strong and em for strikethrough\nconst _punctuationGfmStrongEm = /(?!~)[\\p{P}\\p{S}]/u;\nconst _punctuationOrSpaceGfmStrongEm = /(?!~)[\\s\\p{P}\\p{S}]/u;\nconst _notPunctuationOrSpaceGfmStrongEm = /(?:[^\\s\\p{P}\\p{S}]|~)/u;\n\n// sequences em should skip over [title](link), `code`, <html>\nconst blockSkip = /\\[[^[\\]]*?\\]\\((?:\\\\.|[^\\\\\\(\\)]|\\((?:\\\\.|[^\\\\\\(\\)])*\\))*\\)|`[^`]*?`|<(?! )[^<>]*?>/g;\n\nconst emStrongLDelimCore = /^(?:\\*+(?:((?!\\*)punct)|[^\\s*]))|^_+(?:((?!_)punct)|([^\\s_]))/;\n\nconst emStrongLDelim = edit(emStrongLDelimCore, 'u')\n  .replace(/punct/g, _punctuation)\n  .getRegex();\n\nconst emStrongLDelimGfm = edit(emStrongLDelimCore, 'u')\n  .replace(/punct/g, _punctuationGfmStrongEm)\n  .getRegex();\n\nconst emStrongRDelimAstCore =\n  '^[^_*]*?__[^_*]*?\\\\*[^_*]*?(?=__)' // Skip orphan inside strong\n+ '|[^*]+(?=[^*])' // Consume to delim\n+ '|(?!\\\\*)punct(\\\\*+)(?=[\\\\s]|$)' // (1) #*** can only be a Right Delimiter\n+ '|notPunctSpace(\\\\*+)(?!\\\\*)(?=punctSpace|$)' // (2) a***#, a*** can only be a Right Delimiter\n+ '|(?!\\\\*)punctSpace(\\\\*+)(?=notPunctSpace)' // (3) #***a, ***a can only be Left Delimiter\n+ '|[\\\\s](\\\\*+)(?!\\\\*)(?=punct)' // (4) ***# can only be Left Delimiter\n+ '|(?!\\\\*)punct(\\\\*+)(?!\\\\*)(?=punct)' // (5) #***# can be either Left or Right Delimiter\n+ '|notPunctSpace(\\\\*+)(?=notPunctSpace)'; // (6) a***a can be either Left or Right Delimiter\n\nconst emStrongRDelimAst = edit(emStrongRDelimAstCore, 'gu')\n  .replace(/notPunctSpace/g, _notPunctuationOrSpace)\n  .replace(/punctSpace/g, _punctuationOrSpace)\n  .replace(/punct/g, _punctuation)\n  .getRegex();\n\nconst emStrongRDelimAstGfm = edit(emStrongRDelimAstCore, 'gu')\n  .replace(/notPunctSpace/g, _notPunctuationOrSpaceGfmStrongEm)\n  .replace(/punctSpace/g, _punctuationOrSpaceGfmStrongEm)\n  .replace(/punct/g, _punctuationGfmStrongEm)\n  .getRegex();\n\n// (6) Not allowed for _\nconst emStrongRDelimUnd = edit(\n  '^[^_*]*?\\\\*\\\\*[^_*]*?_[^_*]*?(?=\\\\*\\\\*)' // Skip orphan inside strong\n+ '|[^_]+(?=[^_])' // Consume to delim\n+ '|(?!_)punct(_+)(?=[\\\\s]|$)' // (1) #___ can only be a Right Delimiter\n+ '|notPunctSpace(_+)(?!_)(?=punctSpace|$)' // (2) a___#, a___ can only be a Right Delimiter\n+ '|(?!_)punctSpace(_+)(?=notPunctSpace)' // (3) #___a, ___a can only be Left Delimiter\n+ '|[\\\\s](_+)(?!_)(?=punct)' // (4) ___# can only be Left Delimiter\n+ '|(?!_)punct(_+)(?!_)(?=punct)', 'gu') // (5) #___# can be either Left or Right Delimiter\n  .replace(/notPunctSpace/g, _notPunctuationOrSpace)\n  .replace(/punctSpace/g, _punctuationOrSpace)\n  .replace(/punct/g, _punctuation)\n  .getRegex();\n\nconst anyPunctuation = edit(/\\\\(punct)/, 'gu')\n  .replace(/punct/g, _punctuation)\n  .getRegex();\n\nconst autolink = edit(/^<(scheme:[^\\s\\x00-\\x1f<>]*|email)>/)\n  .replace('scheme', /[a-zA-Z][a-zA-Z0-9+.-]{1,31}/)\n  .replace('email', /[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/)\n  .getRegex();\n\nconst _inlineComment = edit(_comment).replace('(?:-->|$)', '-->').getRegex();\nconst tag = edit(\n  '^comment'\n    + '|^</[a-zA-Z][\\\\w:-]*\\\\s*>' // self-closing tag\n    + '|^<[a-zA-Z][\\\\w-]*(?:attribute)*?\\\\s*/?>' // open tag\n    + '|^<\\\\?[\\\\s\\\\S]*?\\\\?>' // processing instruction, e.g. <?php ?>\n    + '|^<![a-zA-Z]+\\\\s[\\\\s\\\\S]*?>' // declaration, e.g. <!DOCTYPE html>\n    + '|^<!\\\\[CDATA\\\\[[\\\\s\\\\S]*?\\\\]\\\\]>') // CDATA section\n  .replace('comment', _inlineComment)\n  .replace('attribute', /\\s+[a-zA-Z:_][\\w.:-]*(?:\\s*=\\s*\"[^\"]*\"|\\s*=\\s*'[^']*'|\\s*=\\s*[^\\s\"'=<>`]+)?/)\n  .getRegex();\n\nconst _inlineLabel = /(?:\\[(?:\\\\.|[^\\[\\]\\\\])*\\]|\\\\.|`[^`]*`|[^\\[\\]\\\\`])*?/;\n\nconst link = edit(/^!?\\[(label)\\]\\(\\s*(href)(?:(?:[ \\t]*(?:\\n[ \\t]*)?)(title))?\\s*\\)/)\n  .replace('label', _inlineLabel)\n  .replace('href', /<(?:\\\\.|[^\\n<>\\\\])+>|[^ \\t\\n\\x00-\\x1f]*/)\n  .replace('title', /\"(?:\\\\\"?|[^\"\\\\])*\"|'(?:\\\\'?|[^'\\\\])*'|\\((?:\\\\\\)?|[^)\\\\])*\\)/)\n  .getRegex();\n\nconst reflink = edit(/^!?\\[(label)\\]\\[(ref)\\]/)\n  .replace('label', _inlineLabel)\n  .replace('ref', _blockLabel)\n  .getRegex();\n\nconst nolink = edit(/^!?\\[(ref)\\](?:\\[\\])?/)\n  .replace('ref', _blockLabel)\n  .getRegex();\n\nconst reflinkSearch = edit('reflink|nolink(?!\\\\()', 'g')\n  .replace('reflink', reflink)\n  .replace('nolink', nolink)\n  .getRegex();\n\n/**\n * Normal Inline Grammar\n */\n\nconst inlineNormal = {\n  _backpedal: noopTest, // only used for GFM url\n  anyPunctuation,\n  autolink,\n  blockSkip,\n  br,\n  code: inlineCode,\n  del: noopTest,\n  emStrongLDelim,\n  emStrongRDelimAst,\n  emStrongRDelimUnd,\n  escape,\n  link,\n  nolink,\n  punctuation,\n  reflink,\n  reflinkSearch,\n  tag,\n  text: inlineText,\n  url: noopTest,\n};\n\ntype InlineKeys = keyof typeof inlineNormal;\n\n/**\n * Pedantic Inline Grammar\n */\n\nconst inlinePedantic: Record<InlineKeys, RegExp> = {\n  ...inlineNormal,\n  link: edit(/^!?\\[(label)\\]\\((.*?)\\)/)\n    .replace('label', _inlineLabel)\n    .getRegex(),\n  reflink: edit(/^!?\\[(label)\\]\\s*\\[([^\\]]*)\\]/)\n    .replace('label', _inlineLabel)\n    .getRegex(),\n};\n\n/**\n * GFM Inline Grammar\n */\n\nconst inlineGfm: Record<InlineKeys, RegExp> = {\n  ...inlineNormal,\n  emStrongRDelimAst: emStrongRDelimAstGfm,\n  emStrongLDelim: emStrongLDelimGfm,\n  url: edit(/^((?:ftp|https?):\\/\\/|www\\.)(?:[a-zA-Z0-9\\-]+\\.?)+[^\\s<]*|^email/, 'i')\n    .replace('email', /[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/)\n    .getRegex(),\n  _backpedal: /(?:[^?!.,:;*_'\"~()&]+|\\([^)]*\\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'\"~)]+(?!$))+/,\n  del: /^(~~?)(?=[^\\s~])((?:\\\\.|[^\\\\])*?(?:\\\\.|[^\\s~\\\\]))\\1(?=[^~]|$)/,\n  text: /^([`~]+|[^`~])(?:(?= {2,}\\n)|(?=[a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-]+@)|[\\s\\S]*?(?:(?=[\\\\<!\\[`*~_]|\\b_|https?:\\/\\/|ftp:\\/\\/|www\\.|$)|[^ ](?= {2,}\\n)|[^a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-](?=[a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-]+@)))/,\n};\n\n/**\n * GFM + Line Breaks Inline Grammar\n */\n\nconst inlineBreaks: Record<InlineKeys, RegExp> = {\n  ...inlineGfm,\n  br: edit(br).replace('{2,}', '*').getRegex(),\n  text: edit(inlineGfm.text)\n    .replace('\\\\b_', '\\\\b_| {2,}\\\\n')\n    .replace(/\\{2,\\}/g, '*')\n    .getRegex(),\n};\n\n/**\n * exports\n */\n\nexport const block = {\n  normal: blockNormal,\n  gfm: blockGfm,\n  pedantic: blockPedantic,\n};\n\nexport const inline = {\n  normal: inlineNormal,\n  gfm: inlineGfm,\n  breaks: inlineBreaks,\n  pedantic: inlinePedantic,\n};\n\nexport interface Rules {\n  other: typeof other\n  block: Record<BlockKeys, RegExp>\n  inline: Record<InlineKeys, RegExp>\n}\n", "import { other } from './rules.ts';\n\n/**\n * Helpers\n */\nconst escapeReplacements: { [index: string]: string } = {\n  '&': '&amp;',\n  '<': '&lt;',\n  '>': '&gt;',\n  '\"': '&quot;',\n  \"'\": '&#39;',\n};\nconst getEscapeReplacement = (ch: string) => escapeReplacements[ch];\n\nexport function escape(html: string, encode?: boolean) {\n  if (encode) {\n    if (other.escapeTest.test(html)) {\n      return html.replace(other.escapeReplace, getEscapeReplacement);\n    }\n  } else {\n    if (other.escapeTestNoEncode.test(html)) {\n      return html.replace(other.escapeReplaceNoEncode, getEscapeReplacement);\n    }\n  }\n\n  return html;\n}\n\nexport function unescape(html: string) {\n  // explicitly match decimal, hex, and named HTML entities\n  return html.replace(other.unescapeTest, (_, n) => {\n    n = n.toLowerCase();\n    if (n === 'colon') return ':';\n    if (n.charAt(0) === '#') {\n      return n.charAt(1) === 'x'\n        ? String.fromCharCode(parseInt(n.substring(2), 16))\n        : String.fromCharCode(+n.substring(1));\n    }\n    return '';\n  });\n}\n\nexport function cleanUrl(href: string) {\n  try {\n    href = encodeURI(href).replace(other.percentDecode, '%');\n  } catch {\n    return null;\n  }\n  return href;\n}\n\nexport function splitCells(tableRow: string, count?: number) {\n  // ensure that every cell-delimiting pipe has a space\n  // before it to distinguish it from an escaped pipe\n  const row = tableRow.replace(other.findPipe, (match, offset, str) => {\n      let escaped = false;\n      let curr = offset;\n      while (--curr >= 0 && str[curr] === '\\\\') escaped = !escaped;\n      if (escaped) {\n        // odd number of slashes means | is escaped\n        // so we leave it alone\n        return '|';\n      } else {\n        // add space before unescaped |\n        return ' |';\n      }\n    }),\n    cells = row.split(other.splitPipe);\n  let i = 0;\n\n  // First/last cell in a row cannot be empty if it has no leading/trailing pipe\n  if (!cells[0].trim()) {\n    cells.shift();\n  }\n  if (cells.length > 0 && !cells.at(-1)?.trim()) {\n    cells.pop();\n  }\n\n  if (count) {\n    if (cells.length > count) {\n      cells.splice(count);\n    } else {\n      while (cells.length < count) cells.push('');\n    }\n  }\n\n  for (; i < cells.length; i++) {\n    // leading or trailing whitespace is ignored per the gfm spec\n    cells[i] = cells[i].trim().replace(other.slashPipe, '|');\n  }\n  return cells;\n}\n\n/**\n * Remove trailing 'c's. Equivalent to str.replace(/c*$/, '').\n * /c*$/ is vulnerable to REDOS.\n *\n * @param str\n * @param c\n * @param invert Remove suffix of non-c chars instead. Default falsey.\n */\nexport function rtrim(str: string, c: string, invert?: boolean) {\n  const l = str.length;\n  if (l === 0) {\n    return '';\n  }\n\n  // Length of suffix matching the invert condition.\n  let suffLen = 0;\n\n  // Step left until we fail to match the invert condition.\n  while (suffLen < l) {\n    const currChar = str.charAt(l - suffLen - 1);\n    if (currChar === c && !invert) {\n      suffLen++;\n    } else if (currChar !== c && invert) {\n      suffLen++;\n    } else {\n      break;\n    }\n  }\n\n  return str.slice(0, l - suffLen);\n}\n\nexport function findClosingBracket(str: string, b: string) {\n  if (str.indexOf(b[1]) === -1) {\n    return -1;\n  }\n\n  let level = 0;\n  for (let i = 0; i < str.length; i++) {\n    if (str[i] === '\\\\') {\n      i++;\n    } else if (str[i] === b[0]) {\n      level++;\n    } else if (str[i] === b[1]) {\n      level--;\n      if (level < 0) {\n        return i;\n      }\n    }\n  }\n  if (level > 0) {\n    return -2;\n  }\n\n  return -1;\n}\n", "import { _defaults } from './defaults.ts';\nimport {\n  rtrim,\n  splitCells,\n  findClosingBracket,\n} from './helpers.ts';\nimport type { Rules } from './rules.ts';\nimport type { _Lexer } from './Lexer.ts';\nimport type { Links, Tokens, Token } from './Tokens.ts';\nimport type { MarkedOptions } from './MarkedOptions.ts';\n\nfunction outputLink(cap: string[], link: Pick<Tokens.Link, 'href' | 'title'>, raw: string, lexer: _Lexer, rules: Rules): Tokens.Link | Tokens.Image {\n  const href = link.href;\n  const title = link.title || null;\n  const text = cap[1].replace(rules.other.outputLinkReplace, '$1');\n\n  lexer.state.inLink = true;\n  const token: Tokens.Link | Tokens.Image = {\n    type: cap[0].charAt(0) === '!' ? 'image' : 'link',\n    raw,\n    href,\n    title,\n    text,\n    tokens: lexer.inlineTokens(text),\n  };\n  lexer.state.inLink = false;\n  return token;\n}\n\nfunction indentCodeCompensation(raw: string, text: string, rules: Rules) {\n  const matchIndentToCode = raw.match(rules.other.indentCodeCompensation);\n\n  if (matchIndentToCode === null) {\n    return text;\n  }\n\n  const indentToCode = matchIndentToCode[1];\n\n  return text\n    .split('\\n')\n    .map(node => {\n      const matchIndentInNode = node.match(rules.other.beginningSpace);\n      if (matchIndentInNode === null) {\n        return node;\n      }\n\n      const [indentInNode] = matchIndentInNode;\n\n      if (indentInNode.length >= indentToCode.length) {\n        return node.slice(indentToCode.length);\n      }\n\n      return node;\n    })\n    .join('\\n');\n}\n\n/**\n * Tokenizer\n */\nexport class _Tokenizer<ParserOutput = string, RendererOutput = string> {\n  options: MarkedOptions<ParserOutput, RendererOutput>;\n  rules!: Rules; // set by the lexer\n  lexer!: _Lexer<ParserOutput, RendererOutput>; // set by the lexer\n\n  constructor(options?: MarkedOptions<ParserOutput, RendererOutput>) {\n    this.options = options || _defaults;\n  }\n\n  space(src: string): Tokens.Space | undefined {\n    const cap = this.rules.block.newline.exec(src);\n    if (cap && cap[0].length > 0) {\n      return {\n        type: 'space',\n        raw: cap[0],\n      };\n    }\n  }\n\n  code(src: string): Tokens.Code | undefined {\n    const cap = this.rules.block.code.exec(src);\n    if (cap) {\n      const text = cap[0].replace(this.rules.other.codeRemoveIndent, '');\n      return {\n        type: 'code',\n        raw: cap[0],\n        codeBlockStyle: 'indented',\n        text: !this.options.pedantic\n          ? rtrim(text, '\\n')\n          : text,\n      };\n    }\n  }\n\n  fences(src: string): Tokens.Code | undefined {\n    const cap = this.rules.block.fences.exec(src);\n    if (cap) {\n      const raw = cap[0];\n      const text = indentCodeCompensation(raw, cap[3] || '', this.rules);\n\n      return {\n        type: 'code',\n        raw,\n        lang: cap[2] ? cap[2].trim().replace(this.rules.inline.anyPunctuation, '$1') : cap[2],\n        text,\n      };\n    }\n  }\n\n  heading(src: string): Tokens.Heading | undefined {\n    const cap = this.rules.block.heading.exec(src);\n    if (cap) {\n      let text = cap[2].trim();\n\n      // remove trailing #s\n      if (this.rules.other.endingHash.test(text)) {\n        const trimmed = rtrim(text, '#');\n        if (this.options.pedantic) {\n          text = trimmed.trim();\n        } else if (!trimmed || this.rules.other.endingSpaceChar.test(trimmed)) {\n          // CommonMark requires space before trailing #s\n          text = trimmed.trim();\n        }\n      }\n\n      return {\n        type: 'heading',\n        raw: cap[0],\n        depth: cap[1].length,\n        text,\n        tokens: this.lexer.inline(text),\n      };\n    }\n  }\n\n  hr(src: string): Tokens.Hr | undefined {\n    const cap = this.rules.block.hr.exec(src);\n    if (cap) {\n      return {\n        type: 'hr',\n        raw: rtrim(cap[0], '\\n'),\n      };\n    }\n  }\n\n  blockquote(src: string): Tokens.Blockquote | undefined {\n    const cap = this.rules.block.blockquote.exec(src);\n    if (cap) {\n      let lines = rtrim(cap[0], '\\n').split('\\n');\n      let raw = '';\n      let text = '';\n      const tokens: Token[] = [];\n\n      while (lines.length > 0) {\n        let inBlockquote = false;\n        const currentLines = [];\n\n        let i;\n        for (i = 0; i < lines.length; i++) {\n          // get lines up to a continuation\n          if (this.rules.other.blockquoteStart.test(lines[i])) {\n            currentLines.push(lines[i]);\n            inBlockquote = true;\n          } else if (!inBlockquote) {\n            currentLines.push(lines[i]);\n          } else {\n            break;\n          }\n        }\n        lines = lines.slice(i);\n\n        const currentRaw = currentLines.join('\\n');\n        const currentText = currentRaw\n          // precede setext continuation with 4 spaces so it isn't a setext\n          .replace(this.rules.other.blockquoteSetextReplace, '\\n    $1')\n          .replace(this.rules.other.blockquoteSetextReplace2, '');\n        raw = raw ? `${raw}\\n${currentRaw}` : currentRaw;\n        text = text ? `${text}\\n${currentText}` : currentText;\n\n        // parse blockquote lines as top level tokens\n        // merge paragraphs if this is a continuation\n        const top = this.lexer.state.top;\n        this.lexer.state.top = true;\n        this.lexer.blockTokens(currentText, tokens, true);\n        this.lexer.state.top = top;\n\n        // if there is no continuation then we are done\n        if (lines.length === 0) {\n          break;\n        }\n\n        const lastToken = tokens.at(-1);\n\n        if (lastToken?.type === 'code') {\n          // blockquote continuation cannot be preceded by a code block\n          break;\n        } else if (lastToken?.type === 'blockquote') {\n          // include continuation in nested blockquote\n          const oldToken = lastToken as Tokens.Blockquote;\n          const newText = oldToken.raw + '\\n' + lines.join('\\n');\n          const newToken = this.blockquote(newText)!;\n          tokens[tokens.length - 1] = newToken;\n\n          raw = raw.substring(0, raw.length - oldToken.raw.length) + newToken.raw;\n          text = text.substring(0, text.length - oldToken.text.length) + newToken.text;\n          break;\n        } else if (lastToken?.type === 'list') {\n          // include continuation in nested list\n          const oldToken = lastToken as Tokens.List;\n          const newText = oldToken.raw + '\\n' + lines.join('\\n');\n          const newToken = this.list(newText)!;\n          tokens[tokens.length - 1] = newToken;\n\n          raw = raw.substring(0, raw.length - lastToken.raw.length) + newToken.raw;\n          text = text.substring(0, text.length - oldToken.raw.length) + newToken.raw;\n          lines = newText.substring(tokens.at(-1)!.raw.length).split('\\n');\n          continue;\n        }\n      }\n\n      return {\n        type: 'blockquote',\n        raw,\n        tokens,\n        text,\n      };\n    }\n  }\n\n  list(src: string): Tokens.List | undefined {\n    let cap = this.rules.block.list.exec(src);\n    if (cap) {\n      let bull = cap[1].trim();\n      const isordered = bull.length > 1;\n\n      const list: Tokens.List = {\n        type: 'list',\n        raw: '',\n        ordered: isordered,\n        start: isordered ? +bull.slice(0, -1) : '',\n        loose: false,\n        items: [],\n      };\n\n      bull = isordered ? `\\\\d{1,9}\\\\${bull.slice(-1)}` : `\\\\${bull}`;\n\n      if (this.options.pedantic) {\n        bull = isordered ? bull : '[*+-]';\n      }\n\n      // Get next list item\n      const itemRegex = this.rules.other.listItemRegex(bull);\n      let endsWithBlankLine = false;\n      // Check if current bullet point can start a new List Item\n      while (src) {\n        let endEarly = false;\n        let raw = '';\n        let itemContents = '';\n        if (!(cap = itemRegex.exec(src))) {\n          break;\n        }\n\n        if (this.rules.block.hr.test(src)) { // End list if bullet was actually HR (possibly move into itemRegex?)\n          break;\n        }\n\n        raw = cap[0];\n        src = src.substring(raw.length);\n\n        let line = cap[2].split('\\n', 1)[0].replace(this.rules.other.listReplaceTabs, (t: string) => ' '.repeat(3 * t.length));\n        let nextLine = src.split('\\n', 1)[0];\n        let blankLine = !line.trim();\n\n        let indent = 0;\n        if (this.options.pedantic) {\n          indent = 2;\n          itemContents = line.trimStart();\n        } else if (blankLine) {\n          indent = cap[1].length + 1;\n        } else {\n          indent = cap[2].search(this.rules.other.nonSpaceChar); // Find first non-space char\n          indent = indent > 4 ? 1 : indent; // Treat indented code blocks (> 4 spaces) as having only 1 indent\n          itemContents = line.slice(indent);\n          indent += cap[1].length;\n        }\n\n        if (blankLine && this.rules.other.blankLine.test(nextLine)) { // Items begin with at most one blank line\n          raw += nextLine + '\\n';\n          src = src.substring(nextLine.length + 1);\n          endEarly = true;\n        }\n\n        if (!endEarly) {\n          const nextBulletRegex = this.rules.other.nextBulletRegex(indent);\n          const hrRegex = this.rules.other.hrRegex(indent);\n          const fencesBeginRegex = this.rules.other.fencesBeginRegex(indent);\n          const headingBeginRegex = this.rules.other.headingBeginRegex(indent);\n          const htmlBeginRegex = this.rules.other.htmlBeginRegex(indent);\n\n          // Check if following lines should be included in List Item\n          while (src) {\n            const rawLine = src.split('\\n', 1)[0];\n            let nextLineWithoutTabs;\n            nextLine = rawLine;\n\n            // Re-align to follow commonmark nesting rules\n            if (this.options.pedantic) {\n              nextLine = nextLine.replace(this.rules.other.listReplaceNesting, '  ');\n              nextLineWithoutTabs = nextLine;\n            } else {\n              nextLineWithoutTabs = nextLine.replace(this.rules.other.tabCharGlobal, '    ');\n            }\n\n            // End list item if found code fences\n            if (fencesBeginRegex.test(nextLine)) {\n              break;\n            }\n\n            // End list item if found start of new heading\n            if (headingBeginRegex.test(nextLine)) {\n              break;\n            }\n\n            // End list item if found start of html block\n            if (htmlBeginRegex.test(nextLine)) {\n              break;\n            }\n\n            // End list item if found start of new bullet\n            if (nextBulletRegex.test(nextLine)) {\n              break;\n            }\n\n            // Horizontal rule found\n            if (hrRegex.test(nextLine)) {\n              break;\n            }\n\n            if (nextLineWithoutTabs.search(this.rules.other.nonSpaceChar) >= indent || !nextLine.trim()) { // Dedent if possible\n              itemContents += '\\n' + nextLineWithoutTabs.slice(indent);\n            } else {\n              // not enough indentation\n              if (blankLine) {\n                break;\n              }\n\n              // paragraph continuation unless last line was a different block level element\n              if (line.replace(this.rules.other.tabCharGlobal, '    ').search(this.rules.other.nonSpaceChar) >= 4) { // indented code block\n                break;\n              }\n              if (fencesBeginRegex.test(line)) {\n                break;\n              }\n              if (headingBeginRegex.test(line)) {\n                break;\n              }\n              if (hrRegex.test(line)) {\n                break;\n              }\n\n              itemContents += '\\n' + nextLine;\n            }\n\n            if (!blankLine && !nextLine.trim()) { // Check if current line is blank\n              blankLine = true;\n            }\n\n            raw += rawLine + '\\n';\n            src = src.substring(rawLine.length + 1);\n            line = nextLineWithoutTabs.slice(indent);\n          }\n        }\n\n        if (!list.loose) {\n          // If the previous item ended with a blank line, the list is loose\n          if (endsWithBlankLine) {\n            list.loose = true;\n          } else if (this.rules.other.doubleBlankLine.test(raw)) {\n            endsWithBlankLine = true;\n          }\n        }\n\n        let istask: RegExpExecArray | null = null;\n        let ischecked: boolean | undefined;\n        // Check for task list items\n        if (this.options.gfm) {\n          istask = this.rules.other.listIsTask.exec(itemContents);\n          if (istask) {\n            ischecked = istask[0] !== '[ ] ';\n            itemContents = itemContents.replace(this.rules.other.listReplaceTask, '');\n          }\n        }\n\n        list.items.push({\n          type: 'list_item',\n          raw,\n          task: !!istask,\n          checked: ischecked,\n          loose: false,\n          text: itemContents,\n          tokens: [],\n        });\n\n        list.raw += raw;\n      }\n\n      // Do not consume newlines at end of final item. Alternatively, make itemRegex *start* with any newlines to simplify/speed up endsWithBlankLine logic\n      const lastItem = list.items.at(-1);\n      if (lastItem) {\n        lastItem.raw = lastItem.raw.trimEnd();\n        lastItem.text = lastItem.text.trimEnd();\n      } else {\n        // not a list since there were no items\n        return;\n      }\n      list.raw = list.raw.trimEnd();\n\n      // Item child tokens handled here at end because we needed to have the final item to trim it first\n      for (let i = 0; i < list.items.length; i++) {\n        this.lexer.state.top = false;\n        list.items[i].tokens = this.lexer.blockTokens(list.items[i].text, []);\n\n        if (!list.loose) {\n          // Check if list should be loose\n          const spacers = list.items[i].tokens.filter(t => t.type === 'space');\n          const hasMultipleLineBreaks = spacers.length > 0 && spacers.some(t => this.rules.other.anyLine.test(t.raw));\n\n          list.loose = hasMultipleLineBreaks;\n        }\n      }\n\n      // Set all items to loose if list is loose\n      if (list.loose) {\n        for (let i = 0; i < list.items.length; i++) {\n          list.items[i].loose = true;\n        }\n      }\n\n      return list;\n    }\n  }\n\n  html(src: string): Tokens.HTML | undefined {\n    const cap = this.rules.block.html.exec(src);\n    if (cap) {\n      const token: Tokens.HTML = {\n        type: 'html',\n        block: true,\n        raw: cap[0],\n        pre: cap[1] === 'pre' || cap[1] === 'script' || cap[1] === 'style',\n        text: cap[0],\n      };\n      return token;\n    }\n  }\n\n  def(src: string): Tokens.Def | undefined {\n    const cap = this.rules.block.def.exec(src);\n    if (cap) {\n      const tag = cap[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal, ' ');\n      const href = cap[2] ? cap[2].replace(this.rules.other.hrefBrackets, '$1').replace(this.rules.inline.anyPunctuation, '$1') : '';\n      const title = cap[3] ? cap[3].substring(1, cap[3].length - 1).replace(this.rules.inline.anyPunctuation, '$1') : cap[3];\n      return {\n        type: 'def',\n        tag,\n        raw: cap[0],\n        href,\n        title,\n      };\n    }\n  }\n\n  table(src: string): Tokens.Table | undefined {\n    const cap = this.rules.block.table.exec(src);\n    if (!cap) {\n      return;\n    }\n\n    if (!this.rules.other.tableDelimiter.test(cap[2])) {\n      // delimiter row must have a pipe (|) or colon (:) otherwise it is a setext heading\n      return;\n    }\n\n    const headers = splitCells(cap[1]);\n    const aligns = cap[2].replace(this.rules.other.tableAlignChars, '').split('|');\n    const rows = cap[3]?.trim() ? cap[3].replace(this.rules.other.tableRowBlankLine, '').split('\\n') : [];\n\n    const item: Tokens.Table = {\n      type: 'table',\n      raw: cap[0],\n      header: [],\n      align: [],\n      rows: [],\n    };\n\n    if (headers.length !== aligns.length) {\n      // header and align columns must be equal, rows can be different.\n      return;\n    }\n\n    for (const align of aligns) {\n      if (this.rules.other.tableAlignRight.test(align)) {\n        item.align.push('right');\n      } else if (this.rules.other.tableAlignCenter.test(align)) {\n        item.align.push('center');\n      } else if (this.rules.other.tableAlignLeft.test(align)) {\n        item.align.push('left');\n      } else {\n        item.align.push(null);\n      }\n    }\n\n    for (let i = 0; i < headers.length; i++) {\n      item.header.push({\n        text: headers[i],\n        tokens: this.lexer.inline(headers[i]),\n        header: true,\n        align: item.align[i],\n      });\n    }\n\n    for (const row of rows) {\n      item.rows.push(splitCells(row, item.header.length).map((cell, i) => {\n        return {\n          text: cell,\n          tokens: this.lexer.inline(cell),\n          header: false,\n          align: item.align[i],\n        };\n      }));\n    }\n\n    return item;\n  }\n\n  lheading(src: string): Tokens.Heading | undefined {\n    const cap = this.rules.block.lheading.exec(src);\n    if (cap) {\n      return {\n        type: 'heading',\n        raw: cap[0],\n        depth: cap[2].charAt(0) === '=' ? 1 : 2,\n        text: cap[1],\n        tokens: this.lexer.inline(cap[1]),\n      };\n    }\n  }\n\n  paragraph(src: string): Tokens.Paragraph | undefined {\n    const cap = this.rules.block.paragraph.exec(src);\n    if (cap) {\n      const text = cap[1].charAt(cap[1].length - 1) === '\\n'\n        ? cap[1].slice(0, -1)\n        : cap[1];\n      return {\n        type: 'paragraph',\n        raw: cap[0],\n        text,\n        tokens: this.lexer.inline(text),\n      };\n    }\n  }\n\n  text(src: string): Tokens.Text | undefined {\n    const cap = this.rules.block.text.exec(src);\n    if (cap) {\n      return {\n        type: 'text',\n        raw: cap[0],\n        text: cap[0],\n        tokens: this.lexer.inline(cap[0]),\n      };\n    }\n  }\n\n  escape(src: string): Tokens.Escape | undefined {\n    const cap = this.rules.inline.escape.exec(src);\n    if (cap) {\n      return {\n        type: 'escape',\n        raw: cap[0],\n        text: cap[1],\n      };\n    }\n  }\n\n  tag(src: string): Tokens.Tag | undefined {\n    const cap = this.rules.inline.tag.exec(src);\n    if (cap) {\n      if (!this.lexer.state.inLink && this.rules.other.startATag.test(cap[0])) {\n        this.lexer.state.inLink = true;\n      } else if (this.lexer.state.inLink && this.rules.other.endATag.test(cap[0])) {\n        this.lexer.state.inLink = false;\n      }\n      if (!this.lexer.state.inRawBlock && this.rules.other.startPreScriptTag.test(cap[0])) {\n        this.lexer.state.inRawBlock = true;\n      } else if (this.lexer.state.inRawBlock && this.rules.other.endPreScriptTag.test(cap[0])) {\n        this.lexer.state.inRawBlock = false;\n      }\n\n      return {\n        type: 'html',\n        raw: cap[0],\n        inLink: this.lexer.state.inLink,\n        inRawBlock: this.lexer.state.inRawBlock,\n        block: false,\n        text: cap[0],\n      };\n    }\n  }\n\n  link(src: string): Tokens.Link | Tokens.Image | undefined {\n    const cap = this.rules.inline.link.exec(src);\n    if (cap) {\n      const trimmedUrl = cap[2].trim();\n      if (!this.options.pedantic && this.rules.other.startAngleBracket.test(trimmedUrl)) {\n        // commonmark requires matching angle brackets\n        if (!(this.rules.other.endAngleBracket.test(trimmedUrl))) {\n          return;\n        }\n\n        // ending angle bracket cannot be escaped\n        const rtrimSlash = rtrim(trimmedUrl.slice(0, -1), '\\\\');\n        if ((trimmedUrl.length - rtrimSlash.length) % 2 === 0) {\n          return;\n        }\n      } else {\n        // find closing parenthesis\n        const lastParenIndex = findClosingBracket(cap[2], '()');\n        if (lastParenIndex === -2) {\n          // more open parens than closed\n          return;\n        }\n\n        if (lastParenIndex > -1) {\n          const start = cap[0].indexOf('!') === 0 ? 5 : 4;\n          const linkLen = start + cap[1].length + lastParenIndex;\n          cap[2] = cap[2].substring(0, lastParenIndex);\n          cap[0] = cap[0].substring(0, linkLen).trim();\n          cap[3] = '';\n        }\n      }\n      let href = cap[2];\n      let title = '';\n      if (this.options.pedantic) {\n        // split pedantic href and title\n        const link = this.rules.other.pedanticHrefTitle.exec(href);\n\n        if (link) {\n          href = link[1];\n          title = link[3];\n        }\n      } else {\n        title = cap[3] ? cap[3].slice(1, -1) : '';\n      }\n\n      href = href.trim();\n      if (this.rules.other.startAngleBracket.test(href)) {\n        if (this.options.pedantic && !(this.rules.other.endAngleBracket.test(trimmedUrl))) {\n          // pedantic allows starting angle bracket without ending angle bracket\n          href = href.slice(1);\n        } else {\n          href = href.slice(1, -1);\n        }\n      }\n      return outputLink(cap, {\n        href: href ? href.replace(this.rules.inline.anyPunctuation, '$1') : href,\n        title: title ? title.replace(this.rules.inline.anyPunctuation, '$1') : title,\n      }, cap[0], this.lexer, this.rules);\n    }\n  }\n\n  reflink(src: string, links: Links): Tokens.Link | Tokens.Image | Tokens.Text | undefined {\n    let cap;\n    if ((cap = this.rules.inline.reflink.exec(src))\n      || (cap = this.rules.inline.nolink.exec(src))) {\n      const linkString = (cap[2] || cap[1]).replace(this.rules.other.multipleSpaceGlobal, ' ');\n      const link = links[linkString.toLowerCase()];\n      if (!link) {\n        const text = cap[0].charAt(0);\n        return {\n          type: 'text',\n          raw: text,\n          text,\n        };\n      }\n      return outputLink(cap, link, cap[0], this.lexer, this.rules);\n    }\n  }\n\n  emStrong(src: string, maskedSrc: string, prevChar = ''): Tokens.Em | Tokens.Strong | undefined {\n    let match = this.rules.inline.emStrongLDelim.exec(src);\n    if (!match) return;\n\n    // _ can't be between two alphanumerics. \\p{L}\\p{N} includes non-english alphabet/numbers as well\n    if (match[3] && prevChar.match(this.rules.other.unicodeAlphaNumeric)) return;\n\n    const nextChar = match[1] || match[2] || '';\n\n    if (!nextChar || !prevChar || this.rules.inline.punctuation.exec(prevChar)) {\n      // unicode Regex counts emoji as 1 char; spread into array for proper count (used multiple times below)\n      const lLength = [...match[0]].length - 1;\n      let rDelim, rLength, delimTotal = lLength, midDelimTotal = 0;\n\n      const endReg = match[0][0] === '*' ? this.rules.inline.emStrongRDelimAst : this.rules.inline.emStrongRDelimUnd;\n      endReg.lastIndex = 0;\n\n      // Clip maskedSrc to same section of string as src (move to lexer?)\n      maskedSrc = maskedSrc.slice(-1 * src.length + lLength);\n\n      while ((match = endReg.exec(maskedSrc)) != null) {\n        rDelim = match[1] || match[2] || match[3] || match[4] || match[5] || match[6];\n\n        if (!rDelim) continue; // skip single * in __abc*abc__\n\n        rLength = [...rDelim].length;\n\n        if (match[3] || match[4]) { // found another Left Delim\n          delimTotal += rLength;\n          continue;\n        } else if (match[5] || match[6]) { // either Left or Right Delim\n          if (lLength % 3 && !((lLength + rLength) % 3)) {\n            midDelimTotal += rLength;\n            continue; // CommonMark Emphasis Rules 9-10\n          }\n        }\n\n        delimTotal -= rLength;\n\n        if (delimTotal > 0) continue; // Haven't found enough closing delimiters\n\n        // Remove extra characters. *a*** -> *a*\n        rLength = Math.min(rLength, rLength + delimTotal + midDelimTotal);\n        // char length can be >1 for unicode characters;\n        const lastCharLength = [...match[0]][0].length;\n        const raw = src.slice(0, lLength + match.index + lastCharLength + rLength);\n\n        // Create `em` if smallest delimiter has odd char count. *a***\n        if (Math.min(lLength, rLength) % 2) {\n          const text = raw.slice(1, -1);\n          return {\n            type: 'em',\n            raw,\n            text,\n            tokens: this.lexer.inlineTokens(text),\n          };\n        }\n\n        // Create 'strong' if smallest delimiter has even char count. **a***\n        const text = raw.slice(2, -2);\n        return {\n          type: 'strong',\n          raw,\n          text,\n          tokens: this.lexer.inlineTokens(text),\n        };\n      }\n    }\n  }\n\n  codespan(src: string): Tokens.Codespan | undefined {\n    const cap = this.rules.inline.code.exec(src);\n    if (cap) {\n      let text = cap[2].replace(this.rules.other.newLineCharGlobal, ' ');\n      const hasNonSpaceChars = this.rules.other.nonSpaceChar.test(text);\n      const hasSpaceCharsOnBothEnds = this.rules.other.startingSpaceChar.test(text) && this.rules.other.endingSpaceChar.test(text);\n      if (hasNonSpaceChars && hasSpaceCharsOnBothEnds) {\n        text = text.substring(1, text.length - 1);\n      }\n      return {\n        type: 'codespan',\n        raw: cap[0],\n        text,\n      };\n    }\n  }\n\n  br(src: string): Tokens.Br | undefined {\n    const cap = this.rules.inline.br.exec(src);\n    if (cap) {\n      return {\n        type: 'br',\n        raw: cap[0],\n      };\n    }\n  }\n\n  del(src: string): Tokens.Del | undefined {\n    const cap = this.rules.inline.del.exec(src);\n    if (cap) {\n      return {\n        type: 'del',\n        raw: cap[0],\n        text: cap[2],\n        tokens: this.lexer.inlineTokens(cap[2]),\n      };\n    }\n  }\n\n  autolink(src: string): Tokens.Link | undefined {\n    const cap = this.rules.inline.autolink.exec(src);\n    if (cap) {\n      let text, href;\n      if (cap[2] === '@') {\n        text = cap[1];\n        href = 'mailto:' + text;\n      } else {\n        text = cap[1];\n        href = text;\n      }\n\n      return {\n        type: 'link',\n        raw: cap[0],\n        text,\n        href,\n        tokens: [\n          {\n            type: 'text',\n            raw: text,\n            text,\n          },\n        ],\n      };\n    }\n  }\n\n  url(src: string): Tokens.Link | undefined {\n    let cap;\n    if (cap = this.rules.inline.url.exec(src)) {\n      let text, href;\n      if (cap[2] === '@') {\n        text = cap[0];\n        href = 'mailto:' + text;\n      } else {\n        // do extended autolink path validation\n        let prevCapZero;\n        do {\n          prevCapZero = cap[0];\n          cap[0] = this.rules.inline._backpedal.exec(cap[0])?.[0] ?? '';\n        } while (prevCapZero !== cap[0]);\n        text = cap[0];\n        if (cap[1] === 'www.') {\n          href = 'http://' + cap[0];\n        } else {\n          href = cap[0];\n        }\n      }\n      return {\n        type: 'link',\n        raw: cap[0],\n        text,\n        href,\n        tokens: [\n          {\n            type: 'text',\n            raw: text,\n            text,\n          },\n        ],\n      };\n    }\n  }\n\n  inlineText(src: string): Tokens.Text | undefined {\n    const cap = this.rules.inline.text.exec(src);\n    if (cap) {\n      const escaped = this.lexer.state.inRawBlock;\n      return {\n        type: 'text',\n        raw: cap[0],\n        text: cap[0],\n        escaped,\n      };\n    }\n  }\n}\n", "import { _Tokenizer } from './Tokenizer.ts';\nimport { _defaults } from './defaults.ts';\nimport { other, block, inline } from './rules.ts';\nimport type { Token, TokensList, Tokens } from './Tokens.ts';\nimport type { MarkedOptions } from './MarkedOptions.ts';\n\n/**\n * Block Lexer\n */\nexport class _Lexer<ParserOutput = string, RendererOutput = string> {\n  tokens: TokensList;\n  options: MarkedOptions<ParserOutput, RendererOutput>;\n  state: {\n    inLink: boolean;\n    inRawBlock: boolean;\n    top: boolean;\n  };\n\n  private tokenizer: _Tokenizer<ParserOutput, RendererOutput>;\n  private inlineQueue: { src: string, tokens: Token[] }[];\n\n  constructor(options?: MarkedOptions<ParserOutput, RendererOutput>) {\n    // TokenList cannot be created in one go\n    this.tokens = [] as unknown as TokensList;\n    this.tokens.links = Object.create(null);\n    this.options = options || _defaults;\n    this.options.tokenizer = this.options.tokenizer || new _Tokenizer<ParserOutput, RendererOutput>();\n    this.tokenizer = this.options.tokenizer;\n    this.tokenizer.options = this.options;\n    this.tokenizer.lexer = this;\n    this.inlineQueue = [];\n    this.state = {\n      inLink: false,\n      inRawBlock: false,\n      top: true,\n    };\n\n    const rules = {\n      other,\n      block: block.normal,\n      inline: inline.normal,\n    };\n\n    if (this.options.pedantic) {\n      rules.block = block.pedantic;\n      rules.inline = inline.pedantic;\n    } else if (this.options.gfm) {\n      rules.block = block.gfm;\n      if (this.options.breaks) {\n        rules.inline = inline.breaks;\n      } else {\n        rules.inline = inline.gfm;\n      }\n    }\n    this.tokenizer.rules = rules;\n  }\n\n  /**\n   * Expose Rules\n   */\n  static get rules() {\n    return {\n      block,\n      inline,\n    };\n  }\n\n  /**\n   * Static Lex Method\n   */\n  static lex<ParserOutput = string, RendererOutput = string>(src: string, options?: MarkedOptions<ParserOutput, RendererOutput>) {\n    const lexer = new _Lexer<ParserOutput, RendererOutput>(options);\n    return lexer.lex(src);\n  }\n\n  /**\n   * Static Lex Inline Method\n   */\n  static lexInline<ParserOutput = string, RendererOutput = string>(src: string, options?: MarkedOptions<ParserOutput, RendererOutput>) {\n    const lexer = new _Lexer<ParserOutput, RendererOutput>(options);\n    return lexer.inlineTokens(src);\n  }\n\n  /**\n   * Preprocessing\n   */\n  lex(src: string) {\n    src = src.replace(other.carriageReturn, '\\n');\n\n    this.blockTokens(src, this.tokens);\n\n    for (let i = 0; i < this.inlineQueue.length; i++) {\n      const next = this.inlineQueue[i];\n      this.inlineTokens(next.src, next.tokens);\n    }\n    this.inlineQueue = [];\n\n    return this.tokens;\n  }\n\n  /**\n   * Lexing\n   */\n  blockTokens(src: string, tokens?: Token[], lastParagraphClipped?: boolean): Token[];\n  blockTokens(src: string, tokens?: TokensList, lastParagraphClipped?: boolean): TokensList;\n  blockTokens(src: string, tokens: Token[] = [], lastParagraphClipped = false) {\n    if (this.options.pedantic) {\n      src = src.replace(other.tabCharGlobal, '    ').replace(other.spaceLine, '');\n    }\n\n    while (src) {\n      let token: Tokens.Generic | undefined;\n\n      if (this.options.extensions?.block?.some((extTokenizer) => {\n        if (token = extTokenizer.call({ lexer: this }, src, tokens)) {\n          src = src.substring(token.raw.length);\n          tokens.push(token);\n          return true;\n        }\n        return false;\n      })) {\n        continue;\n      }\n\n      // newline\n      if (token = this.tokenizer.space(src)) {\n        src = src.substring(token.raw.length);\n        const lastToken = tokens.at(-1);\n        if (token.raw.length === 1 && lastToken !== undefined) {\n          // if there's a single \\n as a spacer, it's terminating the last line,\n          // so move it there so that we don't get unnecessary paragraph tags\n          lastToken.raw += '\\n';\n        } else {\n          tokens.push(token);\n        }\n        continue;\n      }\n\n      // code\n      if (token = this.tokenizer.code(src)) {\n        src = src.substring(token.raw.length);\n        const lastToken = tokens.at(-1);\n        // An indented code block cannot interrupt a paragraph.\n        if (lastToken?.type === 'paragraph' || lastToken?.type === 'text') {\n          lastToken.raw += '\\n' + token.raw;\n          lastToken.text += '\\n' + token.text;\n          this.inlineQueue.at(-1)!.src = lastToken.text;\n        } else {\n          tokens.push(token);\n        }\n        continue;\n      }\n\n      // fences\n      if (token = this.tokenizer.fences(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // heading\n      if (token = this.tokenizer.heading(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // hr\n      if (token = this.tokenizer.hr(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // blockquote\n      if (token = this.tokenizer.blockquote(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // list\n      if (token = this.tokenizer.list(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // html\n      if (token = this.tokenizer.html(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // def\n      if (token = this.tokenizer.def(src)) {\n        src = src.substring(token.raw.length);\n        const lastToken = tokens.at(-1);\n        if (lastToken?.type === 'paragraph' || lastToken?.type === 'text') {\n          lastToken.raw += '\\n' + token.raw;\n          lastToken.text += '\\n' + token.raw;\n          this.inlineQueue.at(-1)!.src = lastToken.text;\n        } else if (!this.tokens.links[token.tag]) {\n          this.tokens.links[token.tag] = {\n            href: token.href,\n            title: token.title,\n          };\n        }\n        continue;\n      }\n\n      // table (gfm)\n      if (token = this.tokenizer.table(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // lheading\n      if (token = this.tokenizer.lheading(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // top-level paragraph\n      // prevent paragraph consuming extensions by clipping 'src' to extension start\n      let cutSrc = src;\n      if (this.options.extensions?.startBlock) {\n        let startIndex = Infinity;\n        const tempSrc = src.slice(1);\n        let tempStart;\n        this.options.extensions.startBlock.forEach((getStartIndex) => {\n          tempStart = getStartIndex.call({ lexer: this }, tempSrc);\n          if (typeof tempStart === 'number' && tempStart >= 0) {\n            startIndex = Math.min(startIndex, tempStart);\n          }\n        });\n        if (startIndex < Infinity && startIndex >= 0) {\n          cutSrc = src.substring(0, startIndex + 1);\n        }\n      }\n      if (this.state.top && (token = this.tokenizer.paragraph(cutSrc))) {\n        const lastToken = tokens.at(-1);\n        if (lastParagraphClipped && lastToken?.type === 'paragraph') {\n          lastToken.raw += '\\n' + token.raw;\n          lastToken.text += '\\n' + token.text;\n          this.inlineQueue.pop();\n          this.inlineQueue.at(-1)!.src = lastToken.text;\n        } else {\n          tokens.push(token);\n        }\n        lastParagraphClipped = cutSrc.length !== src.length;\n        src = src.substring(token.raw.length);\n        continue;\n      }\n\n      // text\n      if (token = this.tokenizer.text(src)) {\n        src = src.substring(token.raw.length);\n        const lastToken = tokens.at(-1);\n        if (lastToken?.type === 'text') {\n          lastToken.raw += '\\n' + token.raw;\n          lastToken.text += '\\n' + token.text;\n          this.inlineQueue.pop();\n          this.inlineQueue.at(-1)!.src = lastToken.text;\n        } else {\n          tokens.push(token);\n        }\n        continue;\n      }\n\n      if (src) {\n        const errMsg = 'Infinite loop on byte: ' + src.charCodeAt(0);\n        if (this.options.silent) {\n          console.error(errMsg);\n          break;\n        } else {\n          throw new Error(errMsg);\n        }\n      }\n    }\n\n    this.state.top = true;\n    return tokens;\n  }\n\n  inline(src: string, tokens: Token[] = []) {\n    this.inlineQueue.push({ src, tokens });\n    return tokens;\n  }\n\n  /**\n   * Lexing/Compiling\n   */\n  inlineTokens(src: string, tokens: Token[] = []): Token[] {\n    // String with links masked to avoid interference with em and strong\n    let maskedSrc = src;\n    let match: RegExpExecArray | null = null;\n\n    // Mask out reflinks\n    if (this.tokens.links) {\n      const links = Object.keys(this.tokens.links);\n      if (links.length > 0) {\n        while ((match = this.tokenizer.rules.inline.reflinkSearch.exec(maskedSrc)) != null) {\n          if (links.includes(match[0].slice(match[0].lastIndexOf('[') + 1, -1))) {\n            maskedSrc = maskedSrc.slice(0, match.index)\n              + '[' + 'a'.repeat(match[0].length - 2) + ']'\n              + maskedSrc.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex);\n          }\n        }\n      }\n    }\n\n    // Mask out escaped characters\n    while ((match = this.tokenizer.rules.inline.anyPunctuation.exec(maskedSrc)) != null) {\n      maskedSrc = maskedSrc.slice(0, match.index) + '++' + maskedSrc.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);\n    }\n\n    // Mask out other blocks\n    while ((match = this.tokenizer.rules.inline.blockSkip.exec(maskedSrc)) != null) {\n      maskedSrc = maskedSrc.slice(0, match.index) + '[' + 'a'.repeat(match[0].length - 2) + ']' + maskedSrc.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);\n    }\n\n    let keepPrevChar = false;\n    let prevChar = '';\n    while (src) {\n      if (!keepPrevChar) {\n        prevChar = '';\n      }\n      keepPrevChar = false;\n\n      let token: Tokens.Generic | undefined;\n\n      // extensions\n      if (this.options.extensions?.inline?.some((extTokenizer) => {\n        if (token = extTokenizer.call({ lexer: this }, src, tokens)) {\n          src = src.substring(token.raw.length);\n          tokens.push(token);\n          return true;\n        }\n        return false;\n      })) {\n        continue;\n      }\n\n      // escape\n      if (token = this.tokenizer.escape(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // tag\n      if (token = this.tokenizer.tag(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // link\n      if (token = this.tokenizer.link(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // reflink, nolink\n      if (token = this.tokenizer.reflink(src, this.tokens.links)) {\n        src = src.substring(token.raw.length);\n        const lastToken = tokens.at(-1);\n        if (token.type === 'text' && lastToken?.type === 'text') {\n          lastToken.raw += token.raw;\n          lastToken.text += token.text;\n        } else {\n          tokens.push(token);\n        }\n        continue;\n      }\n\n      // em & strong\n      if (token = this.tokenizer.emStrong(src, maskedSrc, prevChar)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // code\n      if (token = this.tokenizer.codespan(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // br\n      if (token = this.tokenizer.br(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // del (gfm)\n      if (token = this.tokenizer.del(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // autolink\n      if (token = this.tokenizer.autolink(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // url (gfm)\n      if (!this.state.inLink && (token = this.tokenizer.url(src))) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // text\n      // prevent inlineText consuming extensions by clipping 'src' to extension start\n      let cutSrc = src;\n      if (this.options.extensions?.startInline) {\n        let startIndex = Infinity;\n        const tempSrc = src.slice(1);\n        let tempStart;\n        this.options.extensions.startInline.forEach((getStartIndex) => {\n          tempStart = getStartIndex.call({ lexer: this }, tempSrc);\n          if (typeof tempStart === 'number' && tempStart >= 0) {\n            startIndex = Math.min(startIndex, tempStart);\n          }\n        });\n        if (startIndex < Infinity && startIndex >= 0) {\n          cutSrc = src.substring(0, startIndex + 1);\n        }\n      }\n      if (token = this.tokenizer.inlineText(cutSrc)) {\n        src = src.substring(token.raw.length);\n        if (token.raw.slice(-1) !== '_') { // Track prevChar before string of ____ started\n          prevChar = token.raw.slice(-1);\n        }\n        keepPrevChar = true;\n        const lastToken = tokens.at(-1);\n        if (lastToken?.type === 'text') {\n          lastToken.raw += token.raw;\n          lastToken.text += token.text;\n        } else {\n          tokens.push(token);\n        }\n        continue;\n      }\n\n      if (src) {\n        const errMsg = 'Infinite loop on byte: ' + src.charCodeAt(0);\n        if (this.options.silent) {\n          console.error(errMsg);\n          break;\n        } else {\n          throw new Error(errMsg);\n        }\n      }\n    }\n\n    return tokens;\n  }\n}\n", "import { _defaults } from './defaults.ts';\nimport {\n  cleanUrl,\n  escape,\n} from './helpers.ts';\nimport { other } from './rules.ts';\nimport type { MarkedOptions } from './MarkedOptions.ts';\nimport type { Tokens } from './Tokens.ts';\nimport type { _Parser } from './Parser.ts';\n\n/**\n * Renderer\n */\nexport class _Renderer<ParserOutput = string, RendererOutput = string> {\n  options: MarkedOptions<ParserOutput, RendererOutput>;\n  parser!: _Parser<ParserOutput, RendererOutput>; // set by the parser\n  constructor(options?: MarkedOptions<ParserOutput, RendererOutput>) {\n    this.options = options || _defaults;\n  }\n\n  space(token: Tokens.Space): RendererOutput {\n    return '' as RendererOutput;\n  }\n\n  code({ text, lang, escaped }: Tokens.Code): RendererOutput {\n    const langString = (lang || '').match(other.notSpaceStart)?.[0];\n\n    const code = text.replace(other.endingNewline, '') + '\\n';\n\n    if (!langString) {\n      return '<pre><code>'\n        + (escaped ? code : escape(code, true))\n        + '</code></pre>\\n' as RendererOutput;\n    }\n\n    return '<pre><code class=\"language-'\n      + escape(langString)\n      + '\">'\n      + (escaped ? code : escape(code, true))\n      + '</code></pre>\\n' as RendererOutput;\n  }\n\n  blockquote({ tokens }: Tokens.Blockquote): RendererOutput {\n    const body = this.parser.parse(tokens);\n    return `<blockquote>\\n${body}</blockquote>\\n` as RendererOutput;\n  }\n\n  html({ text }: Tokens.HTML | Tokens.Tag): RendererOutput {\n    return text as RendererOutput;\n  }\n\n  heading({ tokens, depth }: Tokens.Heading): RendererOutput {\n    return `<h${depth}>${this.parser.parseInline(tokens)}</h${depth}>\\n` as RendererOutput;\n  }\n\n  hr(token: Tokens.Hr): RendererOutput {\n    return '<hr>\\n' as RendererOutput;\n  }\n\n  list(token: Tokens.List): RendererOutput {\n    const ordered = token.ordered;\n    const start = token.start;\n\n    let body = '';\n    for (let j = 0; j < token.items.length; j++) {\n      const item = token.items[j];\n      body += this.listitem(item);\n    }\n\n    const type = ordered ? 'ol' : 'ul';\n    const startAttr = (ordered && start !== 1) ? (' start=\"' + start + '\"') : '';\n    return '<' + type + startAttr + '>\\n' + body + '</' + type + '>\\n' as RendererOutput;\n  }\n\n  listitem(item: Tokens.ListItem): RendererOutput {\n    let itemBody = '';\n    if (item.task) {\n      const checkbox = this.checkbox({ checked: !!item.checked });\n      if (item.loose) {\n        if (item.tokens[0]?.type === 'paragraph') {\n          item.tokens[0].text = checkbox + ' ' + item.tokens[0].text;\n          if (item.tokens[0].tokens && item.tokens[0].tokens.length > 0 && item.tokens[0].tokens[0].type === 'text') {\n            item.tokens[0].tokens[0].text = checkbox + ' ' + escape(item.tokens[0].tokens[0].text);\n            item.tokens[0].tokens[0].escaped = true;\n          }\n        } else {\n          item.tokens.unshift({\n            type: 'text',\n            raw: checkbox + ' ',\n            text: checkbox + ' ',\n            escaped: true,\n          });\n        }\n      } else {\n        itemBody += checkbox + ' ';\n      }\n    }\n\n    itemBody += this.parser.parse(item.tokens, !!item.loose);\n\n    return `<li>${itemBody}</li>\\n` as RendererOutput;\n  }\n\n  checkbox({ checked }: Tokens.Checkbox): RendererOutput {\n    return '<input '\n      + (checked ? 'checked=\"\" ' : '')\n      + 'disabled=\"\" type=\"checkbox\">' as RendererOutput;\n  }\n\n  paragraph({ tokens }: Tokens.Paragraph): RendererOutput {\n    return `<p>${this.parser.parseInline(tokens)}</p>\\n` as RendererOutput;\n  }\n\n  table(token: Tokens.Table): RendererOutput {\n    let header = '';\n\n    // header\n    let cell = '';\n    for (let j = 0; j < token.header.length; j++) {\n      cell += this.tablecell(token.header[j]);\n    }\n    header += this.tablerow({ text: cell as ParserOutput });\n\n    let body = '';\n    for (let j = 0; j < token.rows.length; j++) {\n      const row = token.rows[j];\n\n      cell = '';\n      for (let k = 0; k < row.length; k++) {\n        cell += this.tablecell(row[k]);\n      }\n\n      body += this.tablerow({ text: cell as ParserOutput });\n    }\n    if (body) body = `<tbody>${body}</tbody>`;\n\n    return '<table>\\n'\n      + '<thead>\\n'\n      + header\n      + '</thead>\\n'\n      + body\n      + '</table>\\n' as RendererOutput;\n  }\n\n  tablerow({ text }: Tokens.TableRow<ParserOutput>): RendererOutput {\n    return `<tr>\\n${text}</tr>\\n` as RendererOutput;\n  }\n\n  tablecell(token: Tokens.TableCell): RendererOutput {\n    const content = this.parser.parseInline(token.tokens);\n    const type = token.header ? 'th' : 'td';\n    const tag = token.align\n      ? `<${type} align=\"${token.align}\">`\n      : `<${type}>`;\n    return tag + content + `</${type}>\\n` as RendererOutput;\n  }\n\n  /**\n   * span level renderer\n   */\n  strong({ tokens }: Tokens.Strong): RendererOutput {\n    return `<strong>${this.parser.parseInline(tokens)}</strong>` as RendererOutput;\n  }\n\n  em({ tokens }: Tokens.Em): RendererOutput {\n    return `<em>${this.parser.parseInline(tokens)}</em>` as RendererOutput;\n  }\n\n  codespan({ text }: Tokens.Codespan): RendererOutput {\n    return `<code>${escape(text, true)}</code>` as RendererOutput;\n  }\n\n  br(token: Tokens.Br): RendererOutput {\n    return '<br>' as RendererOutput;\n  }\n\n  del({ tokens }: Tokens.Del): RendererOutput {\n    return `<del>${this.parser.parseInline(tokens)}</del>` as RendererOutput;\n  }\n\n  link({ href, title, tokens }: Tokens.Link): RendererOutput {\n    const text = this.parser.parseInline(tokens) as string;\n    const cleanHref = cleanUrl(href);\n    if (cleanHref === null) {\n      return text as RendererOutput;\n    }\n    href = cleanHref;\n    let out = '<a href=\"' + href + '\"';\n    if (title) {\n      out += ' title=\"' + (escape(title)) + '\"';\n    }\n    out += '>' + text + '</a>';\n    return out as RendererOutput;\n  }\n\n  image({ href, title, text, tokens }: Tokens.Image): RendererOutput {\n    if (tokens) {\n      text = this.parser.parseInline(tokens, this.parser.textRenderer) as string;\n    }\n    const cleanHref = cleanUrl(href);\n    if (cleanHref === null) {\n      return escape(text) as RendererOutput;\n    }\n    href = cleanHref;\n\n    let out = `<img src=\"${href}\" alt=\"${text}\"`;\n    if (title) {\n      out += ` title=\"${escape(title)}\"`;\n    }\n    out += '>';\n    return out as RendererOutput;\n  }\n\n  text(token: Tokens.Text | Tokens.Escape): RendererOutput {\n    return 'tokens' in token && token.tokens\n      ? this.parser.parseInline(token.tokens) as unknown as RendererOutput\n      : ('escaped' in token && token.escaped ? token.text as RendererOutput : escape(token.text) as RendererOutput);\n  }\n}\n", "import type { Tokens } from './Tokens.ts';\n\n/**\n * Text<PERSON><PERSON>er\n * returns only the textual part of the token\n */\nexport class _TextRenderer<RendererOutput = string> {\n  // no need for block level renderers\n  strong({ text }: Tokens.Strong): RendererOutput {\n    return text as RendererOutput;\n  }\n\n  em({ text }: Tokens.Em): RendererOutput {\n    return text as RendererOutput;\n  }\n\n  codespan({ text }: Tokens.Codespan): RendererOutput {\n    return text as RendererOutput;\n  }\n\n  del({ text }: Tokens.Del): RendererOutput {\n    return text as RendererOutput;\n  }\n\n  html({ text }: Tokens.HTML | Tokens.Tag): RendererOutput {\n    return text as RendererOutput;\n  }\n\n  text({ text }: Tokens.Text | Tokens.Escape | Tokens.Tag): RendererOutput {\n    return text as RendererOutput;\n  }\n\n  link({ text }: Tokens.Link): RendererOutput {\n    return '' + text as RendererOutput;\n  }\n\n  image({ text }: Tokens.Image): RendererOutput {\n    return '' + text as RendererOutput;\n  }\n\n  br(): RendererOutput {\n    return '' as RendererOutput;\n  }\n}\n", "import { _Renderer } from './Renderer.ts';\nimport { _TextRenderer } from './TextRenderer.ts';\nimport { _defaults } from './defaults.ts';\nimport type { MarkedToken, Token, Tokens } from './Tokens.ts';\nimport type { MarkedOptions } from './MarkedOptions.ts';\n\n/**\n * Parsing & Compiling\n */\nexport class _Parser<ParserOutput = string, RendererOutput = string> {\n  options: MarkedOptions<ParserOutput, RendererOutput>;\n  renderer: _Renderer<ParserOutput, RendererOutput>;\n  textRenderer: _TextRenderer<RendererOutput>;\n  constructor(options?: MarkedOptions<ParserOutput, RendererOutput>) {\n    this.options = options || _defaults;\n    this.options.renderer = this.options.renderer || new _Renderer<ParserOutput, RendererOutput>();\n    this.renderer = this.options.renderer;\n    this.renderer.options = this.options;\n    this.renderer.parser = this;\n    this.textRenderer = new _TextRenderer<RendererOutput>();\n  }\n\n  /**\n   * Static Parse Method\n   */\n  static parse<ParserOutput = string, RendererOutput = string>(tokens: Token[], options?: MarkedOptions<ParserOutput, RendererOutput>) {\n    const parser = new _Parser<ParserOutput, RendererOutput>(options);\n    return parser.parse(tokens);\n  }\n\n  /**\n   * Static Parse Inline Method\n   */\n  static parseInline<ParserOutput = string, RendererOutput = string>(tokens: Token[], options?: MarkedOptions<ParserOutput, RendererOutput>) {\n    const parser = new _Parser<ParserOutput, RendererOutput>(options);\n    return parser.parseInline(tokens);\n  }\n\n  /**\n   * Parse Loop\n   */\n  parse(tokens: Token[], top = true): ParserOutput {\n    let out = '';\n\n    for (let i = 0; i < tokens.length; i++) {\n      const anyToken = tokens[i];\n\n      // Run any renderer extensions\n      if (this.options.extensions?.renderers?.[anyToken.type]) {\n        const genericToken = anyToken as Tokens.Generic;\n        const ret = this.options.extensions.renderers[genericToken.type].call({ parser: this }, genericToken);\n        if (ret !== false || !['space', 'hr', 'heading', 'code', 'table', 'blockquote', 'list', 'html', 'paragraph', 'text'].includes(genericToken.type)) {\n          out += ret || '';\n          continue;\n        }\n      }\n\n      const token = anyToken as MarkedToken;\n\n      switch (token.type) {\n        case 'space': {\n          out += this.renderer.space(token);\n          continue;\n        }\n        case 'hr': {\n          out += this.renderer.hr(token);\n          continue;\n        }\n        case 'heading': {\n          out += this.renderer.heading(token);\n          continue;\n        }\n        case 'code': {\n          out += this.renderer.code(token);\n          continue;\n        }\n        case 'table': {\n          out += this.renderer.table(token);\n          continue;\n        }\n        case 'blockquote': {\n          out += this.renderer.blockquote(token);\n          continue;\n        }\n        case 'list': {\n          out += this.renderer.list(token);\n          continue;\n        }\n        case 'html': {\n          out += this.renderer.html(token);\n          continue;\n        }\n        case 'paragraph': {\n          out += this.renderer.paragraph(token);\n          continue;\n        }\n        case 'text': {\n          let textToken = token;\n          let body = this.renderer.text(textToken) as string;\n          while (i + 1 < tokens.length && tokens[i + 1].type === 'text') {\n            textToken = tokens[++i] as Tokens.Text;\n            body += ('\\n' + this.renderer.text(textToken));\n          }\n          if (top) {\n            out += this.renderer.paragraph({\n              type: 'paragraph',\n              raw: body,\n              text: body,\n              tokens: [{ type: 'text', raw: body, text: body, escaped: true }],\n            });\n          } else {\n            out += body;\n          }\n          continue;\n        }\n\n        default: {\n          const errMsg = 'Token with \"' + token.type + '\" type was not found.';\n          if (this.options.silent) {\n            console.error(errMsg);\n            return '' as ParserOutput;\n          } else {\n            throw new Error(errMsg);\n          }\n        }\n      }\n    }\n\n    return out as ParserOutput;\n  }\n\n  /**\n   * Parse Inline Tokens\n   */\n  parseInline(tokens: Token[], renderer: _Renderer<ParserOutput, RendererOutput> | _TextRenderer<RendererOutput> = this.renderer): ParserOutput {\n    let out = '';\n\n    for (let i = 0; i < tokens.length; i++) {\n      const anyToken = tokens[i];\n\n      // Run any renderer extensions\n      if (this.options.extensions?.renderers?.[anyToken.type]) {\n        const ret = this.options.extensions.renderers[anyToken.type].call({ parser: this }, anyToken);\n        if (ret !== false || !['escape', 'html', 'link', 'image', 'strong', 'em', 'codespan', 'br', 'del', 'text'].includes(anyToken.type)) {\n          out += ret || '';\n          continue;\n        }\n      }\n\n      const token = anyToken as MarkedToken;\n\n      switch (token.type) {\n        case 'escape': {\n          out += renderer.text(token);\n          break;\n        }\n        case 'html': {\n          out += renderer.html(token);\n          break;\n        }\n        case 'link': {\n          out += renderer.link(token);\n          break;\n        }\n        case 'image': {\n          out += renderer.image(token);\n          break;\n        }\n        case 'strong': {\n          out += renderer.strong(token);\n          break;\n        }\n        case 'em': {\n          out += renderer.em(token);\n          break;\n        }\n        case 'codespan': {\n          out += renderer.codespan(token);\n          break;\n        }\n        case 'br': {\n          out += renderer.br(token);\n          break;\n        }\n        case 'del': {\n          out += renderer.del(token);\n          break;\n        }\n        case 'text': {\n          out += renderer.text(token);\n          break;\n        }\n        default: {\n          const errMsg = 'Token with \"' + token.type + '\" type was not found.';\n          if (this.options.silent) {\n            console.error(errMsg);\n            return '' as ParserOutput;\n          } else {\n            throw new Error(errMsg);\n          }\n        }\n      }\n    }\n    return out as ParserOutput;\n  }\n}\n", "import { _defaults } from './defaults.ts';\nimport { _Lexer } from './Lexer.ts';\nimport { _Parser } from './Parser.ts';\nimport type { MarkedOptions } from './MarkedOptions.ts';\nimport type { Token, TokensList } from './Tokens.ts';\n\nexport class _Hooks<ParserOutput = string, RendererOutput = string> {\n  options: MarkedOptions<ParserOutput, RendererOutput>;\n  block?: boolean;\n\n  constructor(options?: MarkedOptions<ParserOutput, RendererOutput>) {\n    this.options = options || _defaults;\n  }\n\n  static passThroughHooks = new Set([\n    'preprocess',\n    'postprocess',\n    'processAllTokens',\n  ]);\n\n  /**\n   * Process markdown before marked\n   */\n  preprocess(markdown: string) {\n    return markdown;\n  }\n\n  /**\n   * Process HTML after marked is finished\n   */\n  postprocess(html: ParserOutput) {\n    return html;\n  }\n\n  /**\n   * Process all tokens before walk tokens\n   */\n  processAllTokens(tokens: Token[] | TokensList) {\n    return tokens;\n  }\n\n  /**\n   * Provide function to tokenize markdown\n   */\n  provideLexer() {\n    return this.block ? _Lexer.lex : _Lexer.lexInline;\n  }\n\n  /**\n   * Provide function to parse tokens\n   */\n  provideParser() {\n    return this.block ? _Parser.parse<ParserOutput, RendererOutput> : _Parser.parseInline<ParserOutput, RendererOutput>;\n  }\n}\n", "import { _getDefaults } from './defaults.ts';\nimport { _Lexer } from './Lexer.ts';\nimport { _Parser } from './Parser.ts';\nimport { _Hooks } from './Hooks.ts';\nimport { _Renderer } from './Renderer.ts';\nimport { _Tokenizer } from './Tokenizer.ts';\nimport { _TextRenderer } from './TextRenderer.ts';\nimport { escape } from './helpers.ts';\nimport type { MarkedExtension, MarkedOptions } from './MarkedOptions.ts';\nimport type { Token, Tokens, TokensList } from './Tokens.ts';\n\nexport type MaybePromise = void | Promise<void>;\n\ntype UnknownFunction = (...args: unknown[]) => unknown;\ntype GenericRendererFunction = (...args: unknown[]) => string | false;\n\nexport class Marked<ParserOutput = string, RendererOutput = string> {\n  defaults = _getDefaults<ParserOutput, RendererOutput>();\n  options = this.setOptions;\n\n  parse = this.parseMarkdown(true);\n  parseInline = this.parseMarkdown(false);\n\n  Parser = _Parser<ParserOutput, RendererOutput>;\n  Renderer = _Renderer<ParserOutput, RendererOutput>;\n  TextRenderer = _TextRenderer<RendererOutput>;\n  Lexer = _Lexer;\n  Tokenizer = _Tokenizer<ParserOutput, RendererOutput>;\n  Hooks = _Hooks<ParserOutput, RendererOutput>;\n\n  constructor(...args: MarkedExtension<ParserOutput, RendererOutput>[]) {\n    this.use(...args);\n  }\n\n  /**\n   * Run callback for every token\n   */\n  walkTokens(tokens: Token[] | TokensList, callback: (token: Token) => MaybePromise | MaybePromise[]) {\n    let values: MaybePromise[] = [];\n    for (const token of tokens) {\n      values = values.concat(callback.call(this, token));\n      switch (token.type) {\n        case 'table': {\n          const tableToken = token as Tokens.Table;\n          for (const cell of tableToken.header) {\n            values = values.concat(this.walkTokens(cell.tokens, callback));\n          }\n          for (const row of tableToken.rows) {\n            for (const cell of row) {\n              values = values.concat(this.walkTokens(cell.tokens, callback));\n            }\n          }\n          break;\n        }\n        case 'list': {\n          const listToken = token as Tokens.List;\n          values = values.concat(this.walkTokens(listToken.items, callback));\n          break;\n        }\n        default: {\n          const genericToken = token as Tokens.Generic;\n          if (this.defaults.extensions?.childTokens?.[genericToken.type]) {\n            this.defaults.extensions.childTokens[genericToken.type].forEach((childTokens) => {\n              const tokens = genericToken[childTokens].flat(Infinity) as Token[] | TokensList;\n              values = values.concat(this.walkTokens(tokens, callback));\n            });\n          } else if (genericToken.tokens) {\n            values = values.concat(this.walkTokens(genericToken.tokens, callback));\n          }\n        }\n      }\n    }\n    return values;\n  }\n\n  use(...args: MarkedExtension<ParserOutput, RendererOutput>[]) {\n    const extensions: MarkedOptions<ParserOutput, RendererOutput>['extensions'] = this.defaults.extensions || { renderers: {}, childTokens: {} };\n\n    args.forEach((pack) => {\n      // copy options to new object\n      const opts = { ...pack } as MarkedOptions<ParserOutput, RendererOutput>;\n\n      // set async to true if it was set to true before\n      opts.async = this.defaults.async || opts.async || false;\n\n      // ==-- Parse \"addon\" extensions --== //\n      if (pack.extensions) {\n        pack.extensions.forEach((ext) => {\n          if (!ext.name) {\n            throw new Error('extension name required');\n          }\n          if ('renderer' in ext) { // Renderer extensions\n            const prevRenderer = extensions.renderers[ext.name];\n            if (prevRenderer) {\n              // Replace extension with func to run new extension but fall back if false\n              extensions.renderers[ext.name] = function(...args) {\n                let ret = ext.renderer.apply(this, args);\n                if (ret === false) {\n                  ret = prevRenderer.apply(this, args);\n                }\n                return ret;\n              };\n            } else {\n              extensions.renderers[ext.name] = ext.renderer;\n            }\n          }\n          if ('tokenizer' in ext) { // Tokenizer Extensions\n            if (!ext.level || (ext.level !== 'block' && ext.level !== 'inline')) {\n              throw new Error(\"extension level must be 'block' or 'inline'\");\n            }\n            const extLevel = extensions[ext.level];\n            if (extLevel) {\n              extLevel.unshift(ext.tokenizer);\n            } else {\n              extensions[ext.level] = [ext.tokenizer];\n            }\n            if (ext.start) { // Function to check for start of token\n              if (ext.level === 'block') {\n                if (extensions.startBlock) {\n                  extensions.startBlock.push(ext.start);\n                } else {\n                  extensions.startBlock = [ext.start];\n                }\n              } else if (ext.level === 'inline') {\n                if (extensions.startInline) {\n                  extensions.startInline.push(ext.start);\n                } else {\n                  extensions.startInline = [ext.start];\n                }\n              }\n            }\n          }\n          if ('childTokens' in ext && ext.childTokens) { // Child tokens to be visited by walkTokens\n            extensions.childTokens[ext.name] = ext.childTokens;\n          }\n        });\n        opts.extensions = extensions;\n      }\n\n      // ==-- Parse \"overwrite\" extensions --== //\n      if (pack.renderer) {\n        const renderer = this.defaults.renderer || new _Renderer<ParserOutput, RendererOutput>(this.defaults);\n        for (const prop in pack.renderer) {\n          if (!(prop in renderer)) {\n            throw new Error(`renderer '${prop}' does not exist`);\n          }\n          if (['options', 'parser'].includes(prop)) {\n            // ignore options property\n            continue;\n          }\n          const rendererProp = prop as Exclude<keyof _Renderer<ParserOutput, RendererOutput>, 'options' | 'parser'>;\n          const rendererFunc = pack.renderer[rendererProp] as GenericRendererFunction;\n          const prevRenderer = renderer[rendererProp] as GenericRendererFunction;\n          // Replace renderer with func to run extension, but fall back if false\n          renderer[rendererProp] = (...args: unknown[]) => {\n            let ret = rendererFunc.apply(renderer, args);\n            if (ret === false) {\n              ret = prevRenderer.apply(renderer, args);\n            }\n            return (ret || '') as RendererOutput;\n          };\n        }\n        opts.renderer = renderer;\n      }\n      if (pack.tokenizer) {\n        const tokenizer = this.defaults.tokenizer || new _Tokenizer<ParserOutput, RendererOutput>(this.defaults);\n        for (const prop in pack.tokenizer) {\n          if (!(prop in tokenizer)) {\n            throw new Error(`tokenizer '${prop}' does not exist`);\n          }\n          if (['options', 'rules', 'lexer'].includes(prop)) {\n            // ignore options, rules, and lexer properties\n            continue;\n          }\n          const tokenizerProp = prop as Exclude<keyof _Tokenizer<ParserOutput, RendererOutput>, 'options' | 'rules' | 'lexer'>;\n          const tokenizerFunc = pack.tokenizer[tokenizerProp] as UnknownFunction;\n          const prevTokenizer = tokenizer[tokenizerProp] as UnknownFunction;\n          // Replace tokenizer with func to run extension, but fall back if false\n          // @ts-expect-error cannot type tokenizer function dynamically\n          tokenizer[tokenizerProp] = (...args: unknown[]) => {\n            let ret = tokenizerFunc.apply(tokenizer, args);\n            if (ret === false) {\n              ret = prevTokenizer.apply(tokenizer, args);\n            }\n            return ret;\n          };\n        }\n        opts.tokenizer = tokenizer;\n      }\n\n      // ==-- Parse Hooks extensions --== //\n      if (pack.hooks) {\n        const hooks = this.defaults.hooks || new _Hooks<ParserOutput, RendererOutput>();\n        for (const prop in pack.hooks) {\n          if (!(prop in hooks)) {\n            throw new Error(`hook '${prop}' does not exist`);\n          }\n          if (['options', 'block'].includes(prop)) {\n            // ignore options and block properties\n            continue;\n          }\n          const hooksProp = prop as Exclude<keyof _Hooks<ParserOutput, RendererOutput>, 'options' | 'block'>;\n          const hooksFunc = pack.hooks[hooksProp] as UnknownFunction;\n          const prevHook = hooks[hooksProp] as UnknownFunction;\n          if (_Hooks.passThroughHooks.has(prop)) {\n            // @ts-expect-error cannot type hook function dynamically\n            hooks[hooksProp] = (arg: unknown) => {\n              if (this.defaults.async) {\n                return Promise.resolve(hooksFunc.call(hooks, arg)).then(ret => {\n                  return prevHook.call(hooks, ret);\n                });\n              }\n\n              const ret = hooksFunc.call(hooks, arg);\n              return prevHook.call(hooks, ret);\n            };\n          } else {\n            // @ts-expect-error cannot type hook function dynamically\n            hooks[hooksProp] = (...args: unknown[]) => {\n              let ret = hooksFunc.apply(hooks, args);\n              if (ret === false) {\n                ret = prevHook.apply(hooks, args);\n              }\n              return ret;\n            };\n          }\n        }\n        opts.hooks = hooks;\n      }\n\n      // ==-- Parse WalkTokens extensions --== //\n      if (pack.walkTokens) {\n        const walkTokens = this.defaults.walkTokens;\n        const packWalktokens = pack.walkTokens;\n        opts.walkTokens = function(token) {\n          let values: MaybePromise[] = [];\n          values.push(packWalktokens.call(this, token));\n          if (walkTokens) {\n            values = values.concat(walkTokens.call(this, token));\n          }\n          return values;\n        };\n      }\n\n      this.defaults = { ...this.defaults, ...opts };\n    });\n\n    return this;\n  }\n\n  setOptions(opt: MarkedOptions<ParserOutput, RendererOutput>) {\n    this.defaults = { ...this.defaults, ...opt };\n    return this;\n  }\n\n  lexer(src: string, options?: MarkedOptions<ParserOutput, RendererOutput>) {\n    return _Lexer.lex(src, options ?? this.defaults);\n  }\n\n  parser(tokens: Token[], options?: MarkedOptions<ParserOutput, RendererOutput>) {\n    return _Parser.parse<ParserOutput, RendererOutput>(tokens, options ?? this.defaults);\n  }\n\n  private parseMarkdown(blockType: boolean) {\n    type overloadedParse = {\n      (src: string, options: MarkedOptions<ParserOutput, RendererOutput> & { async: true }): Promise<ParserOutput>;\n      (src: string, options: MarkedOptions<ParserOutput, RendererOutput> & { async: false }): ParserOutput;\n      (src: string, options?: MarkedOptions<ParserOutput, RendererOutput> | null): ParserOutput | Promise<ParserOutput>;\n    };\n\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    const parse: overloadedParse = (src: string, options?: MarkedOptions<ParserOutput, RendererOutput> | null): any => {\n      const origOpt = { ...options };\n      const opt = { ...this.defaults, ...origOpt };\n\n      const throwError = this.onError(!!opt.silent, !!opt.async);\n\n      // throw error if an extension set async to true but parse was called with async: false\n      if (this.defaults.async === true && origOpt.async === false) {\n        return throwError(new Error('marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise.'));\n      }\n\n      // throw error in case of non string input\n      if (typeof src === 'undefined' || src === null) {\n        return throwError(new Error('marked(): input parameter is undefined or null'));\n      }\n      if (typeof src !== 'string') {\n        return throwError(new Error('marked(): input parameter is of type '\n          + Object.prototype.toString.call(src) + ', string expected'));\n      }\n\n      if (opt.hooks) {\n        opt.hooks.options = opt;\n        opt.hooks.block = blockType;\n      }\n\n      const lexer = opt.hooks ? opt.hooks.provideLexer() : (blockType ? _Lexer.lex : _Lexer.lexInline);\n      const parser = opt.hooks ? opt.hooks.provideParser() : (blockType ? _Parser.parse : _Parser.parseInline);\n\n      if (opt.async) {\n        return Promise.resolve(opt.hooks ? opt.hooks.preprocess(src) : src)\n          .then(src => lexer(src, opt))\n          .then(tokens => opt.hooks ? opt.hooks.processAllTokens(tokens) : tokens)\n          .then(tokens => opt.walkTokens ? Promise.all(this.walkTokens(tokens, opt.walkTokens)).then(() => tokens) : tokens)\n          .then(tokens => parser(tokens, opt))\n          .then(html => opt.hooks ? opt.hooks.postprocess(html) : html)\n          .catch(throwError);\n      }\n\n      try {\n        if (opt.hooks) {\n          src = opt.hooks.preprocess(src) as string;\n        }\n        let tokens = lexer(src, opt);\n        if (opt.hooks) {\n          tokens = opt.hooks.processAllTokens(tokens);\n        }\n        if (opt.walkTokens) {\n          this.walkTokens(tokens, opt.walkTokens);\n        }\n        let html = parser(tokens, opt);\n        if (opt.hooks) {\n          html = opt.hooks.postprocess(html);\n        }\n        return html;\n      } catch(e) {\n        return throwError(e as Error);\n      }\n    };\n\n    return parse;\n  }\n\n  private onError(silent: boolean, async: boolean) {\n    return (e: Error): string | Promise<string> => {\n      e.message += '\\nPlease report this to https://github.com/markedjs/marked.';\n\n      if (silent) {\n        const msg = '<p>An error occurred:</p><pre>'\n          + escape(e.message + '', true)\n          + '</pre>';\n        if (async) {\n          return Promise.resolve(msg);\n        }\n        return msg;\n      }\n\n      if (async) {\n        return Promise.reject(e);\n      }\n      throw e;\n    };\n  }\n}\n", "import { _<PERSON>er } from './Lexer.ts';\nimport { _Parser } from './Parser.ts';\nimport { _Tokenizer } from './Tokenizer.ts';\nimport { _Renderer } from './Renderer.ts';\nimport { _TextRenderer } from './TextRenderer.ts';\nimport { _Hooks } from './Hooks.ts';\nimport { Marked } from './Instance.ts';\nimport {\n  _getDefaults,\n  changeDefaults,\n  _defaults,\n} from './defaults.ts';\nimport type { MarkedExtension, MarkedOptions } from './MarkedOptions.ts';\nimport type { Token, TokensList } from './Tokens.ts';\nimport type { MaybePromise } from './Instance.ts';\n\nconst markedInstance = new Marked();\n\n/**\n * Compiles markdown to HTML asynchronously.\n *\n * @param src String of markdown source to be compiled\n * @param options Hash of options, having async: true\n * @return Promise of string of compiled HTML\n */\nexport function marked(src: string, options: MarkedOptions & { async: true }): Promise<string>;\n\n/**\n * Compiles markdown to HTML.\n *\n * @param src String of markdown source to be compiled\n * @param options Optional hash of options\n * @return String of compiled HTML. Will be a Promise of string if async is set to true by any extensions.\n */\nexport function marked(src: string, options: MarkedOptions & { async: false }): string;\nexport function marked(src: string, options: MarkedOptions & { async: true }): Promise<string>;\nexport function marked(src: string, options?: MarkedOptions | null): string | Promise<string>;\nexport function marked(src: string, opt?: MarkedOptions | null): string | Promise<string> {\n  return markedInstance.parse(src, opt);\n}\n\n/**\n * Sets the default options.\n *\n * @param options Hash of options\n */\nmarked.options =\nmarked.setOptions = function(options: MarkedOptions) {\n  markedInstance.setOptions(options);\n  marked.defaults = markedInstance.defaults;\n  changeDefaults(marked.defaults);\n  return marked;\n};\n\n/**\n * Gets the original marked default options.\n */\nmarked.getDefaults = _getDefaults;\n\nmarked.defaults = _defaults;\n\n/**\n * Use Extension\n */\n\nmarked.use = function(...args: MarkedExtension[]) {\n  markedInstance.use(...args);\n  marked.defaults = markedInstance.defaults;\n  changeDefaults(marked.defaults);\n  return marked;\n};\n\n/**\n * Run callback for every token\n */\n\nmarked.walkTokens = function(tokens: Token[] | TokensList, callback: (token: Token) => MaybePromise | MaybePromise[]) {\n  return markedInstance.walkTokens(tokens, callback);\n};\n\n/**\n * Compiles markdown to HTML without enclosing `p` tag.\n *\n * @param src String of markdown source to be compiled\n * @param options Hash of options\n * @return String of compiled HTML\n */\nmarked.parseInline = markedInstance.parseInline;\n\n/**\n * Expose\n */\nmarked.Parser = _Parser;\nmarked.parser = _Parser.parse;\nmarked.Renderer = _Renderer;\nmarked.TextRenderer = _TextRenderer;\nmarked.Lexer = _Lexer;\nmarked.lexer = _Lexer.lex;\nmarked.Tokenizer = _Tokenizer;\nmarked.Hooks = _Hooks;\nmarked.parse = marked;\n\nexport const options = marked.options;\nexport const setOptions = marked.setOptions;\nexport const use = marked.use;\nexport const walkTokens = marked.walkTokens;\nexport const parseInline = marked.parseInline;\nexport const parse = marked;\nexport const parser = _Parser.parse;\nexport const lexer = _Lexer.lex;\nexport { _defaults as defaults, _getDefaults as getDefaults } from './defaults.ts';\nexport { _Lexer as Lexer } from './Lexer.ts';\nexport { _Parser as Parser } from './Parser.ts';\nexport { _Tokenizer as Tokenizer } from './Tokenizer.ts';\nexport { _Renderer as Renderer } from './Renderer.ts';\nexport { _TextRenderer as TextRenderer } from './TextRenderer.ts';\nexport { _Hooks as Hooks } from './Hooks.ts';\nexport { Marked } from './Instance.ts';\nexport type * from './MarkedOptions.ts';\nexport type * from './Tokens.ts';\n"], "mappings": ";;;;;AAKO,SAASA,IAA4G;AAC1H,SAAO,EACL,OAAO,OACP,QAAQ,OACR,YAAY,MACZ,KAAK,MACL,OAAO,MACP,UAAU,OACV,UAAU,MACV,QAAQ,OACR,WAAW,MACX,YAAY,KACd;AACF;AAEO,IAAIC,IAAqCD,EAAa;AAEtD,SAASE,EAA+DC,IAA0D;AACvIF,MAAYE;AACd;ACxBA,IAAMC,IAAW,EAAE,MAAM,MAAM,KAAK;AAEpC,SAASC,EAAKC,IAAwBC,IAAM,IAAI;AAC9C,MAAIC,IAAS,OAAOF,MAAU,WAAWA,KAAQA,GAAM,QACjDG,IAAM,EACV,SAAS,CAACC,GAAuBC,MAAyB;AACxD,QAAIC,IAAY,OAAOD,KAAQ,WAAWA,IAAMA,EAAI;AACpD,WAAAC,IAAYA,EAAU,QAAQC,EAAM,OAAO,IAAI,GAC/CL,IAASA,EAAO,QAAQE,GAAME,CAAS,GAChCH;EACT,GACA,UAAU,MACD,IAAI,OAAOD,GAAQD,CAAG,EAEjC;AACA,SAAOE;AACT;AAEO,IAAMI,IAAQ,EACnB,kBAAkB,0BAClB,mBAAmB,eACnB,wBAAwB,iBACxB,gBAAgB,QAChB,YAAY,MACZ,mBAAmB,MACnB,iBAAiB,MACjB,cAAc,QACd,mBAAmB,OACnB,eAAe,OACf,qBAAqB,QACrB,WAAW,YACX,iBAAiB,qBACjB,iBAAiB,YACjB,yBAAyB,kCACzB,0BAA0B,oBAC1B,iBAAiB,QACjB,oBAAoB,2BACpB,YAAY,eACZ,iBAAiB,gBACjB,SAAS,UACT,cAAc,YACd,gBAAgB,QAChB,iBAAiB,cACjB,mBAAmB,aACnB,iBAAiB,aACjB,kBAAkB,cAClB,gBAAgB,aAChB,WAAW,SACX,SAAS,WACT,mBAAmB,kCACnB,iBAAiB,oCACjB,mBAAmB,MACnB,iBAAiB,MACjB,mBAAmB,iCACnB,qBAAqB,iBACrB,YAAY,WACZ,eAAe,YACf,oBAAoB,qDACpB,uBAAuB,sDACvB,cAAc,8CACd,OAAO,gBACP,eAAe,QACf,UAAU,OACV,WAAW,OACX,WAAW,SACX,gBAAgB,YAChB,WAAW,UACX,eAAe,QACf,eAAe,OACf,eAAgBC,CAAAA,OAAiB,IAAI,OAAO,WAAWA,EAAI,8BAA+B,GAC1F,iBAAkBC,CAAAA,OAAmB,IAAI,OAAO,QAAQ,KAAK,IAAI,GAAGA,KAAS,CAAC,CAAC,oDAAqD,GACpI,SAAUA,CAAAA,OAAmB,IAAI,OAAO,QAAQ,KAAK,IAAI,GAAGA,KAAS,CAAC,CAAC,oDAAoD,GAC3H,kBAAmBA,CAAAA,OAAmB,IAAI,OAAO,QAAQ,KAAK,IAAI,GAAGA,KAAS,CAAC,CAAC,iBAAiB,GACjG,mBAAoBA,CAAAA,OAAmB,IAAI,OAAO,QAAQ,KAAK,IAAI,GAAGA,KAAS,CAAC,CAAC,IAAI,GACrF,gBAAiBA,CAAAA,OAAmB,IAAI,OAAO,QAAQ,KAAK,IAAI,GAAGA,KAAS,CAAC,CAAC,sBAAsB,GAAG,EACzG;AAzDO,IA+DDC,KAAU;AA/DT,IAgEDC,KAAY;AAhEX,IAiEDC,KAAS;AAjER,IAkEDC,IAAK;AAlEJ,IAmEDC,KAAU;AAnET,IAoEDC,IAAS;AApER,IAqEDC,KAAe;AArEd,IAsEDC,KAAWlB,EAAKiB,EAAY,EAC/B,QAAQ,SAASD,CAAM,EACvB,QAAQ,cAAc,mBAAmB,EACzC,QAAQ,WAAW,uBAAuB,EAC1C,QAAQ,eAAe,SAAS,EAChC,QAAQ,YAAY,cAAc,EAClC,QAAQ,SAAS,mBAAmB,EACpC,QAAQ,YAAY,EAAE,EACtB,SAAS;AA9EL,IA+EDG,KAAcnB,EAAKiB,EAAY,EAClC,QAAQ,SAASD,CAAM,EACvB,QAAQ,cAAc,mBAAmB,EACzC,QAAQ,WAAW,uBAAuB,EAC1C,QAAQ,eAAe,SAAS,EAChC,QAAQ,YAAY,cAAc,EAClC,QAAQ,SAAS,mBAAmB,EACpC,QAAQ,UAAU,mCAAmC,EACrD,SAAS;AAvFL,IAwFDI,IAAa;AAxFZ,IAyFDC,KAAY;AAzFX,IA0FDC,IAAc;AA1Fb,IA2FDC,KAAMvB,EAAK,6GAA6G,EAC3H,QAAQ,SAASsB,CAAW,EAC5B,QAAQ,SAAS,8DAA8D,EAC/E,SAAS;AA9FL,IAgGDE,KAAOxB,EAAK,sCAAsC,EACrD,QAAQ,SAASgB,CAAM,EACvB,SAAS;AAlGL,IAoGDS,IAAO;AApGN,IA0GDC,IAAW;AA1GV,IA2GDC,KAAO3B,EACX,6dASK,GAAG,EACP,QAAQ,WAAW0B,CAAQ,EAC3B,QAAQ,OAAOD,CAAI,EACnB,QAAQ,aAAa,0EAA0E,EAC/F,SAAS;AAzHL,IA2HDG,KAAY5B,EAAKoB,CAAU,EAC9B,QAAQ,MAAMN,CAAE,EAChB,QAAQ,WAAW,uBAAuB,EAC1C,QAAQ,aAAa,EAAE,EACvB,QAAQ,UAAU,EAAE,EACpB,QAAQ,cAAc,SAAS,EAC/B,QAAQ,UAAU,gDAAgD,EAClE,QAAQ,QAAQ,wBAAwB,EACxC,QAAQ,QAAQ,6DAA6D,EAC7E,QAAQ,OAAOW,CAAI,EACnB,SAAS;AArIL,IAuIDI,KAAa7B,EAAK,yCAAyC,EAC9D,QAAQ,aAAa4B,EAAS,EAC9B,SAAS;AAzIL,IA+IDE,IAAc,EAClB,YAAAD,IACA,MAAMjB,IACN,KAAAW,IACA,QAAAV,IACA,SAAAE,IACA,IAAAD,GACA,MAAAa,IACA,UAAAT,IACA,MAAAM,IACA,SAAAb,IACA,WAAAiB,IACA,OAAO7B,GACP,MAAMsB,GACR;AA7JO,IAqKDU,KAAW/B,EACf,6JAEsF,EACrF,QAAQ,MAAMc,CAAE,EAChB,QAAQ,WAAW,uBAAuB,EAC1C,QAAQ,cAAc,SAAS,EAC/B,QAAQ,QAAQ,wBAAyB,EACzC,QAAQ,UAAU,gDAAgD,EAClE,QAAQ,QAAQ,wBAAwB,EACxC,QAAQ,QAAQ,6DAA6D,EAC7E,QAAQ,OAAOW,CAAI,EACnB,SAAS;AAjLL,IAmLDO,KAAsC,EAC1C,GAAGF,GACH,UAAUX,IACV,OAAOY,IACP,WAAW/B,EAAKoB,CAAU,EACvB,QAAQ,MAAMN,CAAE,EAChB,QAAQ,WAAW,uBAAuB,EAC1C,QAAQ,aAAa,EAAE,EACvB,QAAQ,SAASiB,EAAQ,EACzB,QAAQ,cAAc,SAAS,EAC/B,QAAQ,UAAU,gDAAgD,EAClE,QAAQ,QAAQ,wBAAwB,EACxC,QAAQ,QAAQ,6DAA6D,EAC7E,QAAQ,OAAON,CAAI,EACnB,SAAS,EACd;AAlMO,IAwMDQ,KAA2C,EAC/C,GAAGH,GACH,MAAM9B,EACJ,wIAEwE,EACvE,QAAQ,WAAW0B,CAAQ,EAC3B,QAAQ,QAAQ,mKAGkB,EAClC,SAAS,GACZ,KAAK,qEACL,SAAS,0BACT,QAAQ3B,GACR,UAAU,oCACV,WAAWC,EAAKoB,CAAU,EACvB,QAAQ,MAAMN,CAAE,EAChB,QAAQ,WAAW;EAAiB,EACpC,QAAQ,YAAYI,EAAQ,EAC5B,QAAQ,UAAU,EAAE,EACpB,QAAQ,cAAc,SAAS,EAC/B,QAAQ,WAAW,EAAE,EACrB,QAAQ,SAAS,EAAE,EACnB,QAAQ,SAAS,EAAE,EACnB,QAAQ,QAAQ,EAAE,EAClB,SAAS,EACd;AAnOO,IAyODgB,KAAS;AAzOR,IA0ODC,KAAa;AA1OZ,IA2ODC,KAAK;AA3OJ,IA4ODC,KAAa;AA5OZ,IA+ODC,IAAe;AA/Od,IAgPDC,IAAsB;AAhPrB,IAiPDC,KAAyB;AAjPxB,IAkPDC,KAAczC,EAAK,yBAAyB,GAAG,EAClD,QAAQ,eAAeuC,CAAmB,EAAE,SAAS;AAnPjD,IAsPDG,KAA0B;AAtPzB,IAuPDC,KAAiC;AAvPhC,IAwPDC,KAAoC;AAxPnC,IA2PDC,KAAY;AA3PX,IA6PDC,KAAqB;AA7PpB,IA+PDC,KAAiB/C,EAAK8C,IAAoB,GAAG,EAChD,QAAQ,UAAUR,CAAY,EAC9B,SAAS;AAjQL,IAmQDU,KAAoBhD,EAAK8C,IAAoB,GAAG,EACnD,QAAQ,UAAUJ,EAAuB,EACzC,SAAS;AArQL,IAuQDO,KACJ;AAxQK,IAiRDC,KAAoBlD,EAAKiD,IAAuB,IAAI,EACvD,QAAQ,kBAAkBT,EAAsB,EAChD,QAAQ,eAAeD,CAAmB,EAC1C,QAAQ,UAAUD,CAAY,EAC9B,SAAS;AArRL,IAuRDa,KAAuBnD,EAAKiD,IAAuB,IAAI,EAC1D,QAAQ,kBAAkBL,EAAiC,EAC3D,QAAQ,eAAeD,EAA8B,EACrD,QAAQ,UAAUD,EAAuB,EACzC,SAAS;AA3RL,IA8RDU,KAAoBpD,EACxB,oNAMiC,IAAI,EACpC,QAAQ,kBAAkBwC,EAAsB,EAChD,QAAQ,eAAeD,CAAmB,EAC1C,QAAQ,UAAUD,CAAY,EAC9B,SAAS;AAzSL,IA2SDe,KAAiBrD,EAAK,aAAa,IAAI,EAC1C,QAAQ,UAAUsC,CAAY,EAC9B,SAAS;AA7SL,IA+SDgB,KAAWtD,EAAK,qCAAqC,EACxD,QAAQ,UAAU,8BAA8B,EAChD,QAAQ,SAAS,8IAA8I,EAC/J,SAAS;AAlTL,IAoTDuD,KAAiBvD,EAAK0B,CAAQ,EAAE,QAAQ,aAAa,KAAK,EAAE,SAAS;AApTpE,IAqTD8B,KAAMxD,EACV,0JAKsC,EACrC,QAAQ,WAAWuD,EAAc,EACjC,QAAQ,aAAa,6EAA6E,EAClG,SAAS;AA9TL,IAgUDE,IAAe;AAhUd,IAkUDC,KAAO1D,EAAK,mEAAmE,EAClF,QAAQ,SAASyD,CAAY,EAC7B,QAAQ,QAAQ,yCAAyC,EACzD,QAAQ,SAAS,6DAA6D,EAC9E,SAAS;AAtUL,IAwUDE,KAAU3D,EAAK,yBAAyB,EAC3C,QAAQ,SAASyD,CAAY,EAC7B,QAAQ,OAAOnC,CAAW,EAC1B,SAAS;AA3UL,IA6UDsC,KAAS5D,EAAK,uBAAuB,EACxC,QAAQ,OAAOsB,CAAW,EAC1B,SAAS;AA/UL,IAiVDuC,KAAgB7D,EAAK,yBAAyB,GAAG,EACpD,QAAQ,WAAW2D,EAAO,EAC1B,QAAQ,UAAUC,EAAM,EACxB,SAAS;AApVL,IA0VDE,IAAe,EACnB,YAAY/D,GACZ,gBAAAsD,IACA,UAAAC,IACA,WAAAT,IACA,IAAAT,IACA,MAAMD,IACN,KAAKpC,GACL,gBAAAgD,IACA,mBAAAG,IACA,mBAAAE,IACA,QAAAlB,IACA,MAAAwB,IACA,QAAAE,IACA,aAAAnB,IACA,SAAAkB,IACA,eAAAE,IACA,KAAAL,IACA,MAAMnB,IACN,KAAKtC,EACP;AA9WO,IAsXDgE,KAA6C,EACjD,GAAGD,GACH,MAAM9D,EAAK,yBAAyB,EACjC,QAAQ,SAASyD,CAAY,EAC7B,SAAS,GACZ,SAASzD,EAAK,+BAA+B,EAC1C,QAAQ,SAASyD,CAAY,EAC7B,SAAS,EACd;AA9XO,IAoYDO,IAAwC,EAC5C,GAAGF,GACH,mBAAmBX,IACnB,gBAAgBH,IAChB,KAAKhD,EAAK,oEAAoE,GAAG,EAC9E,QAAQ,SAAS,2EAA2E,EAC5F,SAAS,GACZ,YAAY,8EACZ,KAAK,iEACL,MAAM,6NACR;AA9YO,IAoZDiE,KAA2C,EAC/C,GAAGD,GACH,IAAIhE,EAAKoC,EAAE,EAAE,QAAQ,QAAQ,GAAG,EAAE,SAAS,GAC3C,MAAMpC,EAAKgE,EAAU,IAAI,EACtB,QAAQ,QAAQ,eAAe,EAC/B,QAAQ,WAAW,GAAG,EACtB,SAAS,EACd;AA3ZO,IAiaME,IAAQ,EACnB,QAAQpC,GACR,KAAKE,IACL,UAAUC,GACZ;AAraO,IAuaMkC,IAAS,EACpB,QAAQL,GACR,KAAKE,GACL,QAAQC,IACR,UAAUF,GACZ;ACzbA,IAAMK,KAAkD,EACtD,KAAK,SACL,KAAK,QACL,KAAK,QACL,KAAK,UACL,KAAK,QACP;AANA,IAOMC,KAAwBC,CAAAA,OAAeF,GAAmBE,EAAE;AAE3D,SAASpC,EAAOP,IAAc4C,GAAkB;AACrD,MAAIA,GAAAA;AACF,QAAI/D,EAAM,WAAW,KAAKmB,EAAI,EAC5B,QAAOA,GAAK,QAAQnB,EAAM,eAAe6D,EAAoB;EAAA,WAG3D7D,EAAM,mBAAmB,KAAKmB,EAAI,EACpC,QAAOA,GAAK,QAAQnB,EAAM,uBAAuB6D,EAAoB;AAIzE,SAAO1C;AACT;AAgBO,SAAS6C,EAASC,IAAc;AACrC,MAAI;AACFA,IAAAA,KAAO,UAAUA,EAAI,EAAE,QAAQjE,EAAM,eAAe,GAAG;EACzD,QAAQ;AACN,WAAO;EACT;AACA,SAAOiE;AACT;AAEO,SAASC,EAAWC,IAAkBC,GAAgB;;AAG3D,MAAMC,IAAMF,GAAS,QAAQnE,EAAM,UAAU,CAACsE,GAAOC,GAAQC,MAAQ;AACjE,QAAIC,IAAU,OACVC,IAAOH;AACX,WAAO,EAAEG,KAAQ,KAAKF,EAAIE,CAAI,MAAM,OAAMD,KAAU,CAACA;AACrD,WAAIA,IAGK,MAGA;EAEX,CAAC,GACDE,IAAQN,EAAI,MAAMrE,EAAM,SAAS,GAC/B4E,IAAI;AAUR,MAPKD,EAAM,CAAC,EAAE,KAAK,KACjBA,EAAM,MAAM,GAEVA,EAAM,SAAS,KAAK,GAACA,MAAAA,EAAM,GAAG,EAAE,MAAXA,gBAAAA,IAAc,WACrCA,EAAM,IAAI,GAGRP,EACF,KAAIO,EAAM,SAASP,EACjBO,GAAM,OAAOP,CAAK;MAElB,QAAOO,EAAM,SAASP,IAAOO,GAAM,KAAK,EAAE;AAI9C,SAAOC,IAAID,EAAM,QAAQC,IAEvBD,GAAMC,CAAC,IAAID,EAAMC,CAAC,EAAE,KAAK,EAAE,QAAQ5E,EAAM,WAAW,GAAG;AAEzD,SAAO2E;AACT;AAUO,SAASE,EAAML,IAAaM,GAAWC,GAAkB;AAC9D,MAAMC,IAAIR,GAAI;AACd,MAAIQ,MAAM,EACR,QAAO;AAIT,MAAIC,IAAU;AAGd,SAAOA,IAAUD,KAAG;AAClB,QAAME,IAAWV,GAAI,OAAOQ,IAAIC,IAAU,CAAC;AAC3C,QAAIC,MAAaJ,KAAK,CAACC,EACrBE;aACSC,MAAaJ,KAAKC,EAC3BE;QAEA;EAEJ;AAEA,SAAOT,GAAI,MAAM,GAAGQ,IAAIC,CAAO;AACjC;AAEO,SAASE,GAAmBX,IAAaY,GAAW;AACzD,MAAIZ,GAAI,QAAQY,EAAE,CAAC,CAAC,MAAM,GACxB,QAAO;AAGT,MAAIC,IAAQ;AACZ,WAAST,IAAI,GAAGA,IAAIJ,GAAI,QAAQI,IAC9B,KAAIJ,GAAII,CAAC,MAAM,KACbA;WACSJ,GAAII,CAAC,MAAMQ,EAAE,CAAC,EACvBC;WACSb,GAAII,CAAC,MAAMQ,EAAE,CAAC,MACvBC,KACIA,IAAQ,GACV,QAAOT;AAIb,SAAIS,IAAQ,IACH,KAGF;AACT;ACzIA,SAASC,GAAWC,IAAerC,GAA2CsC,GAAaC,GAAeC,GAA0C;AAClJ,MAAMzB,IAAOf,EAAK,MACZyC,IAAQzC,EAAK,SAAS,MACtB0C,IAAOL,GAAI,CAAC,EAAE,QAAQG,EAAM,MAAM,mBAAmB,IAAI;AAE/DD,IAAM,MAAM,SAAS;AACrB,MAAMI,IAAoC,EACxC,MAAMN,GAAI,CAAC,EAAE,OAAO,CAAC,MAAM,MAAM,UAAU,QAC3C,KAAAC,GACA,MAAAvB,GACA,OAAA0B,GACA,MAAAC,GACA,QAAQH,EAAM,aAAaG,CAAI,EACjC;AACA,SAAAH,EAAM,MAAM,SAAS,OACdI;AACT;AAEA,SAASC,GAAuBN,IAAaI,GAAcF,GAAc;AACvE,MAAMK,IAAoBP,GAAI,MAAME,EAAM,MAAM,sBAAsB;AAEtE,MAAIK,MAAsB,KACxB,QAAOH;AAGT,MAAMI,IAAeD,EAAkB,CAAC;AAExC,SAAOH,EACJ,MAAM;CAAI,EACV,IAAIK,OAAQ;AACX,QAAMC,IAAoBD,EAAK,MAAMP,EAAM,MAAM,cAAc;AAC/D,QAAIQ,MAAsB,KACxB,QAAOD;AAGT,QAAM,CAACE,CAAY,IAAID;AAEvB,WAAIC,EAAa,UAAUH,EAAa,SAC/BC,EAAK,MAAMD,EAAa,MAAM,IAGhCC;EACT,CAAC,EACA,KAAK;CAAI;AACd;AAKO,IAAMG,IAAN,MAAiE;EAKtE,YAAYC,GAAuD;AAJnE;AACA;AACA;AAGE,SAAK,UAAUA,KAAWjH;EAC5B;EAEA,MAAMkH,GAAuC;AAC3C,QAAMf,IAAM,KAAK,MAAM,MAAM,QAAQ,KAAKe,CAAG;AAC7C,QAAIf,KAAOA,EAAI,CAAC,EAAE,SAAS,EACzB,QAAO,EACL,MAAM,SACN,KAAKA,EAAI,CAAC,EACZ;EAEJ;EAEA,KAAKe,GAAsC;AACzC,QAAMf,IAAM,KAAK,MAAM,MAAM,KAAK,KAAKe,CAAG;AAC1C,QAAIf,GAAK;AACP,UAAMK,IAAOL,EAAI,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM,kBAAkB,EAAE;AACjE,aAAO,EACL,MAAM,QACN,KAAKA,EAAI,CAAC,GACV,gBAAgB,YAChB,MAAO,KAAK,QAAQ,WAEhBK,IADAf,EAAMe,GAAM;CAAI,EAEtB;IACF;EACF;EAEA,OAAOU,GAAsC;AAC3C,QAAMf,IAAM,KAAK,MAAM,MAAM,OAAO,KAAKe,CAAG;AAC5C,QAAIf,GAAK;AACP,UAAMC,IAAMD,EAAI,CAAC,GACXK,IAAOE,GAAuBN,GAAKD,EAAI,CAAC,KAAK,IAAI,KAAK,KAAK;AAEjE,aAAO,EACL,MAAM,QACN,KAAAC,GACA,MAAMD,EAAI,CAAC,IAAIA,EAAI,CAAC,EAAE,KAAK,EAAE,QAAQ,KAAK,MAAM,OAAO,gBAAgB,IAAI,IAAIA,EAAI,CAAC,GACpF,MAAAK,EACF;IACF;EACF;EAEA,QAAQU,GAAyC;AAC/C,QAAMf,IAAM,KAAK,MAAM,MAAM,QAAQ,KAAKe,CAAG;AAC7C,QAAIf,GAAK;AACP,UAAIK,IAAOL,EAAI,CAAC,EAAE,KAAK;AAGvB,UAAI,KAAK,MAAM,MAAM,WAAW,KAAKK,CAAI,GAAG;AAC1C,YAAMW,IAAU1B,EAAMe,GAAM,GAAG;AAAA,SAC3B,KAAK,QAAQ,YAEN,CAACW,KAAW,KAAK,MAAM,MAAM,gBAAgB,KAAKA,CAAO,OAElEX,IAAOW,EAAQ,KAAK;MAExB;AAEA,aAAO,EACL,MAAM,WACN,KAAKhB,EAAI,CAAC,GACV,OAAOA,EAAI,CAAC,EAAE,QACd,MAAAK,GACA,QAAQ,KAAK,MAAM,OAAOA,CAAI,EAChC;IACF;EACF;EAEA,GAAGU,GAAoC;AACrC,QAAMf,IAAM,KAAK,MAAM,MAAM,GAAG,KAAKe,CAAG;AACxC,QAAIf,EACF,QAAO,EACL,MAAM,MACN,KAAKV,EAAMU,EAAI,CAAC,GAAG;CAAI,EACzB;EAEJ;EAEA,WAAWe,GAA4C;AACrD,QAAMf,IAAM,KAAK,MAAM,MAAM,WAAW,KAAKe,CAAG;AAChD,QAAIf,GAAK;AACP,UAAIiB,IAAQ3B,EAAMU,EAAI,CAAC,GAAG;CAAI,EAAE,MAAM;CAAI,GACtCC,IAAM,IACNI,IAAO,IACLa,IAAkB,CAAC;AAEzB,aAAOD,EAAM,SAAS,KAAG;AACvB,YAAIE,IAAe,OACbC,IAAe,CAAC,GAElB/B;AACJ,aAAKA,IAAI,GAAGA,IAAI4B,EAAM,QAAQ5B,IAE5B,KAAI,KAAK,MAAM,MAAM,gBAAgB,KAAK4B,EAAM5B,CAAC,CAAC,EAChD+B,GAAa,KAAKH,EAAM5B,CAAC,CAAC,GAC1B8B,IAAe;iBACN,CAACA,EACVC,GAAa,KAAKH,EAAM5B,CAAC,CAAC;YAE1B;AAGJ4B,YAAQA,EAAM,MAAM5B,CAAC;AAErB,YAAMgC,IAAaD,EAAa,KAAK;CAAI,GACnCE,IAAcD,EAEjB,QAAQ,KAAK,MAAM,MAAM,yBAAyB;OAAU,EAC5D,QAAQ,KAAK,MAAM,MAAM,0BAA0B,EAAE;AACxDpB,YAAMA,IAAM,GAAGA,CAAG;EAAKoB,CAAU,KAAKA,GACtChB,IAAOA,IAAO,GAAGA,CAAI;EAAKiB,CAAW,KAAKA;AAI1C,YAAMC,IAAM,KAAK,MAAM,MAAM;AAM7B,YALA,KAAK,MAAM,MAAM,MAAM,MACvB,KAAK,MAAM,YAAYD,GAAaJ,GAAQ,IAAI,GAChD,KAAK,MAAM,MAAM,MAAMK,GAGnBN,EAAM,WAAW,EACnB;AAGF,YAAMO,IAAYN,EAAO,GAAG,EAAE;AAE9B,aAAIM,uBAAW,UAAS,OAEtB;AACK,aAAIA,uBAAW,UAAS,cAAc;AAE3C,cAAMC,IAAWD,GACXE,IAAUD,EAAS,MAAM;IAAOR,EAAM,KAAK;CAAI,GAC/CU,IAAW,KAAK,WAAWD,CAAO;AACxCR,YAAOA,EAAO,SAAS,CAAC,IAAIS,GAE5B1B,IAAMA,EAAI,UAAU,GAAGA,EAAI,SAASwB,EAAS,IAAI,MAAM,IAAIE,EAAS,KACpEtB,IAAOA,EAAK,UAAU,GAAGA,EAAK,SAASoB,EAAS,KAAK,MAAM,IAAIE,EAAS;AACxE;QACF,YAAWH,uBAAW,UAAS,QAAQ;AAErC,cAAMC,IAAWD,GACXE,IAAUD,EAAS,MAAM;IAAOR,EAAM,KAAK;CAAI,GAC/CU,IAAW,KAAK,KAAKD,CAAO;AAClCR,YAAOA,EAAO,SAAS,CAAC,IAAIS,GAE5B1B,IAAMA,EAAI,UAAU,GAAGA,EAAI,SAASuB,EAAU,IAAI,MAAM,IAAIG,EAAS,KACrEtB,IAAOA,EAAK,UAAU,GAAGA,EAAK,SAASoB,EAAS,IAAI,MAAM,IAAIE,EAAS,KACvEV,IAAQS,EAAQ,UAAUR,EAAO,GAAG,EAAE,EAAG,IAAI,MAAM,EAAE,MAAM;CAAI;AAC/D;QACF;MACF;AAEA,aAAO,EACL,MAAM,cACN,KAAAjB,GACA,QAAAiB,GACA,MAAAb,EACF;IACF;EACF;EAEA,KAAKU,GAAsC;AACzC,QAAIf,IAAM,KAAK,MAAM,MAAM,KAAK,KAAKe,CAAG;AACxC,QAAIf,GAAK;AACP,UAAItF,IAAOsF,EAAI,CAAC,EAAE,KAAK,GACjB4B,IAAYlH,EAAK,SAAS,GAE1Be,IAAoB,EACxB,MAAM,QACN,KAAK,IACL,SAASmG,GACT,OAAOA,IAAY,CAAClH,EAAK,MAAM,GAAG,EAAE,IAAI,IACxC,OAAO,OACP,OAAO,CAAC,EACV;AAEAA,UAAOkH,IAAY,aAAalH,EAAK,MAAM,EAAE,CAAC,KAAK,KAAKA,CAAI,IAExD,KAAK,QAAQ,aACfA,IAAOkH,IAAYlH,IAAO;AAI5B,UAAMmH,IAAY,KAAK,MAAM,MAAM,cAAcnH,CAAI,GACjDoH,IAAoB;AAExB,aAAOf,KAAK;AACV,YAAIgB,IAAW,OACX9B,IAAM,IACN+B,IAAe;AAKnB,YAJI,EAAEhC,IAAM6B,EAAU,KAAKd,CAAG,MAI1B,KAAK,MAAM,MAAM,GAAG,KAAKA,CAAG,EAC9B;AAGFd,YAAMD,EAAI,CAAC,GACXe,IAAMA,EAAI,UAAUd,EAAI,MAAM;AAE9B,YAAIgC,IAAOjC,EAAI,CAAC,EAAE,MAAM;GAAM,CAAC,EAAE,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM,iBAAkBkC,OAAc,IAAI,OAAO,IAAIA,EAAE,MAAM,CAAC,GACjHC,IAAWpB,EAAI,MAAM;GAAM,CAAC,EAAE,CAAC,GAC/BqB,IAAY,CAACH,EAAK,KAAK,GAEvBtH,IAAS;AAmBb,YAlBI,KAAK,QAAQ,YACfA,IAAS,GACTqH,IAAeC,EAAK,UAAU,KACrBG,IACTzH,IAASqF,EAAI,CAAC,EAAE,SAAS,KAEzBrF,IAASqF,EAAI,CAAC,EAAE,OAAO,KAAK,MAAM,MAAM,YAAY,GACpDrF,IAASA,IAAS,IAAI,IAAIA,GAC1BqH,IAAeC,EAAK,MAAMtH,CAAM,GAChCA,KAAUqF,EAAI,CAAC,EAAE,SAGfoC,KAAa,KAAK,MAAM,MAAM,UAAU,KAAKD,CAAQ,MACvDlC,KAAOkC,IAAW;GAClBpB,IAAMA,EAAI,UAAUoB,EAAS,SAAS,CAAC,GACvCJ,IAAW,OAGT,CAACA,GAAU;AACb,cAAMM,IAAkB,KAAK,MAAM,MAAM,gBAAgB1H,CAAM,GACzD2H,KAAU,KAAK,MAAM,MAAM,QAAQ3H,CAAM,GACzC4H,KAAmB,KAAK,MAAM,MAAM,iBAAiB5H,CAAM,GAC3D6H,KAAoB,KAAK,MAAM,MAAM,kBAAkB7H,CAAM,GAC7D8H,KAAiB,KAAK,MAAM,MAAM,eAAe9H,CAAM;AAG7D,iBAAOoG,KAAK;AACV,gBAAM2B,IAAU3B,EAAI,MAAM;GAAM,CAAC,EAAE,CAAC,GAChC4B;AAgCJ,gBA/BAR,IAAWO,GAGP,KAAK,QAAQ,YACfP,IAAWA,EAAS,QAAQ,KAAK,MAAM,MAAM,oBAAoB,IAAI,GACrEQ,IAAsBR,KAEtBQ,IAAsBR,EAAS,QAAQ,KAAK,MAAM,MAAM,eAAe,MAAM,GAI3EI,GAAiB,KAAKJ,CAAQ,KAK9BK,GAAkB,KAAKL,CAAQ,KAK/BM,GAAe,KAAKN,CAAQ,KAK5BE,EAAgB,KAAKF,CAAQ,KAK7BG,GAAQ,KAAKH,CAAQ,EACvB;AAGF,gBAAIQ,EAAoB,OAAO,KAAK,MAAM,MAAM,YAAY,KAAKhI,KAAU,CAACwH,EAAS,KAAK,EACxFH,MAAgB;IAAOW,EAAoB,MAAMhI,CAAM;iBAClD;AAgBL,kBAdIyH,KAKAH,EAAK,QAAQ,KAAK,MAAM,MAAM,eAAe,MAAM,EAAE,OAAO,KAAK,MAAM,MAAM,YAAY,KAAK,KAG9FM,GAAiB,KAAKN,CAAI,KAG1BO,GAAkB,KAAKP,CAAI,KAG3BK,GAAQ,KAAKL,CAAI,EACnB;AAGFD,mBAAgB;IAAOG;YACzB;AAEI,aAACC,KAAa,CAACD,EAAS,KAAK,MAC/BC,IAAY,OAGdnC,KAAOyC,IAAU;GACjB3B,IAAMA,EAAI,UAAU2B,EAAQ,SAAS,CAAC,GACtCT,IAAOU,EAAoB,MAAMhI,CAAM;UACzC;QACF;AAEKc,UAAK,UAEJqG,IACFrG,EAAK,QAAQ,OACJ,KAAK,MAAM,MAAM,gBAAgB,KAAKwE,CAAG,MAClD6B,IAAoB;AAIxB,YAAIc,IAAiC,MACjCC;AAEA,aAAK,QAAQ,QACfD,IAAS,KAAK,MAAM,MAAM,WAAW,KAAKZ,CAAY,GAClDY,MACFC,IAAYD,EAAO,CAAC,MAAM,QAC1BZ,IAAeA,EAAa,QAAQ,KAAK,MAAM,MAAM,iBAAiB,EAAE,KAI5EvG,EAAK,MAAM,KAAK,EACd,MAAM,aACN,KAAAwE,GACA,MAAM,CAAC,CAAC2C,GACR,SAASC,GACT,OAAO,OACP,MAAMb,GACN,QAAQ,CAAC,EACX,CAAC,GAEDvG,EAAK,OAAOwE;MACd;AAGA,UAAM6C,IAAWrH,EAAK,MAAM,GAAG,EAAE;AACjC,UAAIqH,EACFA,GAAS,MAAMA,EAAS,IAAI,QAAQ,GACpCA,EAAS,OAAOA,EAAS,KAAK,QAAQ;UAGtC;AAEFrH,QAAK,MAAMA,EAAK,IAAI,QAAQ;AAG5B,eAAS4D,IAAI,GAAGA,IAAI5D,EAAK,MAAM,QAAQ4D,IAIrC,KAHA,KAAK,MAAM,MAAM,MAAM,OACvB5D,EAAK,MAAM4D,CAAC,EAAE,SAAS,KAAK,MAAM,YAAY5D,EAAK,MAAM4D,CAAC,EAAE,MAAM,CAAC,CAAC,GAEhE,CAAC5D,EAAK,OAAO;AAEf,YAAMsH,IAAUtH,EAAK,MAAM4D,CAAC,EAAE,OAAO,OAAO6C,OAAKA,EAAE,SAAS,OAAO,GAC7Dc,IAAwBD,EAAQ,SAAS,KAAKA,EAAQ,KAAKb,OAAK,KAAK,MAAM,MAAM,QAAQ,KAAKA,EAAE,GAAG,CAAC;AAE1GzG,UAAK,QAAQuH;MACf;AAIF,UAAIvH,EAAK,MACP,UAAS4D,IAAI,GAAGA,IAAI5D,EAAK,MAAM,QAAQ4D,IACrC5D,GAAK,MAAM4D,CAAC,EAAE,QAAQ;AAI1B,aAAO5D;IACT;EACF;EAEA,KAAKsF,GAAsC;AACzC,QAAMf,IAAM,KAAK,MAAM,MAAM,KAAK,KAAKe,CAAG;AAC1C,QAAIf,EAQF,QAP2B,EACzB,MAAM,QACN,OAAO,MACP,KAAKA,EAAI,CAAC,GACV,KAAKA,EAAI,CAAC,MAAM,SAASA,EAAI,CAAC,MAAM,YAAYA,EAAI,CAAC,MAAM,SAC3D,MAAMA,EAAI,CAAC,EACb;EAGJ;EAEA,IAAIe,GAAqC;AACvC,QAAMf,IAAM,KAAK,MAAM,MAAM,IAAI,KAAKe,CAAG;AACzC,QAAIf,GAAK;AACP,UAAMvC,IAAMuC,EAAI,CAAC,EAAE,YAAY,EAAE,QAAQ,KAAK,MAAM,MAAM,qBAAqB,GAAG,GAC5EtB,IAAOsB,EAAI,CAAC,IAAIA,EAAI,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM,cAAc,IAAI,EAAE,QAAQ,KAAK,MAAM,OAAO,gBAAgB,IAAI,IAAI,IACtHI,IAAQJ,EAAI,CAAC,IAAIA,EAAI,CAAC,EAAE,UAAU,GAAGA,EAAI,CAAC,EAAE,SAAS,CAAC,EAAE,QAAQ,KAAK,MAAM,OAAO,gBAAgB,IAAI,IAAIA,EAAI,CAAC;AACrH,aAAO,EACL,MAAM,OACN,KAAAvC,GACA,KAAKuC,EAAI,CAAC,GACV,MAAAtB,GACA,OAAA0B,EACF;IACF;EACF;EAEA,MAAMW,GAAuC;;AAC3C,QAAMf,IAAM,KAAK,MAAM,MAAM,MAAM,KAAKe,CAAG;AAK3C,QAJI,CAACf,KAID,CAAC,KAAK,MAAM,MAAM,eAAe,KAAKA,EAAI,CAAC,CAAC,EAE9C;AAGF,QAAMiD,IAAUtE,EAAWqB,EAAI,CAAC,CAAC,GAC3BkD,IAASlD,EAAI,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM,iBAAiB,EAAE,EAAE,MAAM,GAAG,GACvEmD,MAAOnD,MAAAA,EAAI,CAAC,MAALA,gBAAAA,IAAQ,UAASA,EAAI,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM,mBAAmB,EAAE,EAAE,MAAM;CAAI,IAAI,CAAC,GAE9FoD,IAAqB,EACzB,MAAM,SACN,KAAKpD,EAAI,CAAC,GACV,QAAQ,CAAC,GACT,OAAO,CAAC,GACR,MAAM,CAAC,EACT;AAEA,QAAIiD,EAAQ,WAAWC,EAAO,QAK9B;AAAA,eAAWG,KAASH,EACd,MAAK,MAAM,MAAM,gBAAgB,KAAKG,CAAK,IAC7CD,EAAK,MAAM,KAAK,OAAO,IACd,KAAK,MAAM,MAAM,iBAAiB,KAAKC,CAAK,IACrDD,EAAK,MAAM,KAAK,QAAQ,IACf,KAAK,MAAM,MAAM,eAAe,KAAKC,CAAK,IACnDD,EAAK,MAAM,KAAK,MAAM,IAEtBA,EAAK,MAAM,KAAK,IAAI;AAIxB,eAAS/D,IAAI,GAAGA,IAAI4D,EAAQ,QAAQ5D,IAClC+D,GAAK,OAAO,KAAK,EACf,MAAMH,EAAQ5D,CAAC,GACf,QAAQ,KAAK,MAAM,OAAO4D,EAAQ5D,CAAC,CAAC,GACpC,QAAQ,MACR,OAAO+D,EAAK,MAAM/D,CAAC,EACrB,CAAC;AAGH,eAAWP,KAAOqE,EAChBC,GAAK,KAAK,KAAKzE,EAAWG,GAAKsE,EAAK,OAAO,MAAM,EAAE,IAAI,CAACE,GAAMjE,OACrD,EACL,MAAMiE,GACN,QAAQ,KAAK,MAAM,OAAOA,CAAI,GAC9B,QAAQ,OACR,OAAOF,EAAK,MAAM/D,CAAC,EACrB,EACD,CAAC;AAGJ,aAAO+D;IAAAA;EACT;EAEA,SAASrC,GAAyC;AAChD,QAAMf,IAAM,KAAK,MAAM,MAAM,SAAS,KAAKe,CAAG;AAC9C,QAAIf,EACF,QAAO,EACL,MAAM,WACN,KAAKA,EAAI,CAAC,GACV,OAAOA,EAAI,CAAC,EAAE,OAAO,CAAC,MAAM,MAAM,IAAI,GACtC,MAAMA,EAAI,CAAC,GACX,QAAQ,KAAK,MAAM,OAAOA,EAAI,CAAC,CAAC,EAClC;EAEJ;EAEA,UAAUe,GAA2C;AACnD,QAAMf,IAAM,KAAK,MAAM,MAAM,UAAU,KAAKe,CAAG;AAC/C,QAAIf,GAAK;AACP,UAAMK,IAAOL,EAAI,CAAC,EAAE,OAAOA,EAAI,CAAC,EAAE,SAAS,CAAC,MAAM;IAC9CA,EAAI,CAAC,EAAE,MAAM,GAAG,EAAE,IAClBA,EAAI,CAAC;AACT,aAAO,EACL,MAAM,aACN,KAAKA,EAAI,CAAC,GACV,MAAAK,GACA,QAAQ,KAAK,MAAM,OAAOA,CAAI,EAChC;IACF;EACF;EAEA,KAAKU,GAAsC;AACzC,QAAMf,IAAM,KAAK,MAAM,MAAM,KAAK,KAAKe,CAAG;AAC1C,QAAIf,EACF,QAAO,EACL,MAAM,QACN,KAAKA,EAAI,CAAC,GACV,MAAMA,EAAI,CAAC,GACX,QAAQ,KAAK,MAAM,OAAOA,EAAI,CAAC,CAAC,EAClC;EAEJ;EAEA,OAAOe,GAAwC;AAC7C,QAAMf,IAAM,KAAK,MAAM,OAAO,OAAO,KAAKe,CAAG;AAC7C,QAAIf,EACF,QAAO,EACL,MAAM,UACN,KAAKA,EAAI,CAAC,GACV,MAAMA,EAAI,CAAC,EACb;EAEJ;EAEA,IAAIe,GAAqC;AACvC,QAAMf,IAAM,KAAK,MAAM,OAAO,IAAI,KAAKe,CAAG;AAC1C,QAAIf,EACF,QAAI,CAAC,KAAK,MAAM,MAAM,UAAU,KAAK,MAAM,MAAM,UAAU,KAAKA,EAAI,CAAC,CAAC,IACpE,KAAK,MAAM,MAAM,SAAS,OACjB,KAAK,MAAM,MAAM,UAAU,KAAK,MAAM,MAAM,QAAQ,KAAKA,EAAI,CAAC,CAAC,MACxE,KAAK,MAAM,MAAM,SAAS,QAExB,CAAC,KAAK,MAAM,MAAM,cAAc,KAAK,MAAM,MAAM,kBAAkB,KAAKA,EAAI,CAAC,CAAC,IAChF,KAAK,MAAM,MAAM,aAAa,OACrB,KAAK,MAAM,MAAM,cAAc,KAAK,MAAM,MAAM,gBAAgB,KAAKA,EAAI,CAAC,CAAC,MACpF,KAAK,MAAM,MAAM,aAAa,QAGzB,EACL,MAAM,QACN,KAAKA,EAAI,CAAC,GACV,QAAQ,KAAK,MAAM,MAAM,QACzB,YAAY,KAAK,MAAM,MAAM,YAC7B,OAAO,OACP,MAAMA,EAAI,CAAC,EACb;EAEJ;EAEA,KAAKe,GAAqD;AACxD,QAAMf,IAAM,KAAK,MAAM,OAAO,KAAK,KAAKe,CAAG;AAC3C,QAAIf,GAAK;AACP,UAAMuD,IAAavD,EAAI,CAAC,EAAE,KAAK;AAC/B,UAAI,CAAC,KAAK,QAAQ,YAAY,KAAK,MAAM,MAAM,kBAAkB,KAAKuD,CAAU,GAAG;AAEjF,YAAI,CAAE,KAAK,MAAM,MAAM,gBAAgB,KAAKA,CAAU,EACpD;AAIF,YAAMC,IAAalE,EAAMiE,EAAW,MAAM,GAAG,EAAE,GAAG,IAAI;AACtD,aAAKA,EAAW,SAASC,EAAW,UAAU,MAAM,EAClD;MAEJ,OAAO;AAEL,YAAMC,IAAiB7D,GAAmBI,EAAI,CAAC,GAAG,IAAI;AACtD,YAAIyD,MAAmB,GAErB;AAGF,YAAIA,IAAiB,IAAI;AAEvB,cAAMC,KADQ1D,EAAI,CAAC,EAAE,QAAQ,GAAG,MAAM,IAAI,IAAI,KACtBA,EAAI,CAAC,EAAE,SAASyD;AACxCzD,YAAI,CAAC,IAAIA,EAAI,CAAC,EAAE,UAAU,GAAGyD,CAAc,GAC3CzD,EAAI,CAAC,IAAIA,EAAI,CAAC,EAAE,UAAU,GAAG0D,CAAO,EAAE,KAAK,GAC3C1D,EAAI,CAAC,IAAI;QACX;MACF;AACA,UAAItB,IAAOsB,EAAI,CAAC,GACZI,IAAQ;AACZ,UAAI,KAAK,QAAQ,UAAU;AAEzB,YAAMzC,IAAO,KAAK,MAAM,MAAM,kBAAkB,KAAKe,CAAI;AAErDf,cACFe,IAAOf,EAAK,CAAC,GACbyC,IAAQzC,EAAK,CAAC;MAElB,MACEyC,KAAQJ,EAAI,CAAC,IAAIA,EAAI,CAAC,EAAE,MAAM,GAAG,EAAE,IAAI;AAGzC,aAAAtB,IAAOA,EAAK,KAAK,GACb,KAAK,MAAM,MAAM,kBAAkB,KAAKA,CAAI,MAC1C,KAAK,QAAQ,YAAY,CAAE,KAAK,MAAM,MAAM,gBAAgB,KAAK6E,CAAU,IAE7E7E,IAAOA,EAAK,MAAM,CAAC,IAEnBA,IAAOA,EAAK,MAAM,GAAG,EAAE,IAGpBqB,GAAWC,GAAK,EACrB,MAAMtB,KAAOA,EAAK,QAAQ,KAAK,MAAM,OAAO,gBAAgB,IAAI,GAChE,OAAO0B,KAAQA,EAAM,QAAQ,KAAK,MAAM,OAAO,gBAAgB,IAAI,EACrE,GAAGJ,EAAI,CAAC,GAAG,KAAK,OAAO,KAAK,KAAK;IACnC;EACF;EAEA,QAAQe,GAAa4C,GAAoE;AACvF,QAAI3D;AACJ,SAAKA,IAAM,KAAK,MAAM,OAAO,QAAQ,KAAKe,CAAG,OACvCf,IAAM,KAAK,MAAM,OAAO,OAAO,KAAKe,CAAG,IAAI;AAC/C,UAAM6C,KAAc5D,EAAI,CAAC,KAAKA,EAAI,CAAC,GAAG,QAAQ,KAAK,MAAM,MAAM,qBAAqB,GAAG,GACjFrC,IAAOgG,EAAMC,EAAW,YAAY,CAAC;AAC3C,UAAI,CAACjG,GAAM;AACT,YAAM0C,IAAOL,EAAI,CAAC,EAAE,OAAO,CAAC;AAC5B,eAAO,EACL,MAAM,QACN,KAAKK,GACL,MAAAA,EACF;MACF;AACA,aAAON,GAAWC,GAAKrC,GAAMqC,EAAI,CAAC,GAAG,KAAK,OAAO,KAAK,KAAK;IAC7D;EACF;EAEA,SAASe,GAAa8C,GAAmBC,IAAW,IAA2C;AAC7F,QAAI/E,IAAQ,KAAK,MAAM,OAAO,eAAe,KAAKgC,CAAG;AAIrD,QAHI,CAAChC,KAGDA,EAAM,CAAC,KAAK+E,EAAS,MAAM,KAAK,MAAM,MAAM,mBAAmB,EAAG;AAItE,QAAI,EAFa/E,EAAM,CAAC,KAAKA,EAAM,CAAC,KAAK,OAExB,CAAC+E,KAAY,KAAK,MAAM,OAAO,YAAY,KAAKA,CAAQ,GAAG;AAE1E,UAAMC,IAAU,CAAC,GAAGhF,EAAM,CAAC,CAAC,EAAE,SAAS,GACnCiF,GAAQC,GAASC,IAAaH,GAASI,IAAgB,GAErDC,IAASrF,EAAM,CAAC,EAAE,CAAC,MAAM,MAAM,KAAK,MAAM,OAAO,oBAAoB,KAAK,MAAM,OAAO;AAM7F,WALAqF,EAAO,YAAY,GAGnBP,IAAYA,EAAU,MAAM,KAAK9C,EAAI,SAASgD,CAAO,IAE7ChF,IAAQqF,EAAO,KAAKP,CAAS,MAAM,QAAM;AAG/C,YAFAG,IAASjF,EAAM,CAAC,KAAKA,EAAM,CAAC,KAAKA,EAAM,CAAC,KAAKA,EAAM,CAAC,KAAKA,EAAM,CAAC,KAAKA,EAAM,CAAC,GAExE,CAACiF,EAAQ;AAIb,YAFAC,IAAU,CAAC,GAAGD,CAAM,EAAE,QAElBjF,EAAM,CAAC,KAAKA,EAAM,CAAC,GAAG;AACxBmF,eAAcD;AACd;QACF,YAAWlF,EAAM,CAAC,KAAKA,EAAM,CAAC,MACxBgF,IAAU,KAAK,GAAGA,IAAUE,KAAW,IAAI;AAC7CE,eAAiBF;AACjB;QACF;AAKF,YAFAC,KAAcD,GAEVC,IAAa,EAAG;AAGpBD,YAAU,KAAK,IAAIA,GAASA,IAAUC,IAAaC,CAAa;AAEhE,YAAME,IAAiB,CAAC,GAAGtF,EAAM,CAAC,CAAC,EAAE,CAAC,EAAE,QAClCkB,IAAMc,EAAI,MAAM,GAAGgD,IAAUhF,EAAM,QAAQsF,IAAiBJ,CAAO;AAGzE,YAAI,KAAK,IAAIF,GAASE,CAAO,IAAI,GAAG;AAClC,cAAM5D,IAAOJ,EAAI,MAAM,GAAG,EAAE;AAC5B,iBAAO,EACL,MAAM,MACN,KAAAA,GACA,MAAAI,GACA,QAAQ,KAAK,MAAM,aAAaA,CAAI,EACtC;QACF;AAGA,YAAMA,IAAOJ,EAAI,MAAM,GAAG,EAAE;AAC5B,eAAO,EACL,MAAM,UACN,KAAAA,GACA,MAAAI,GACA,QAAQ,KAAK,MAAM,aAAaA,CAAI,EACtC;MACF;IACF;EACF;EAEA,SAASU,GAA0C;AACjD,QAAMf,IAAM,KAAK,MAAM,OAAO,KAAK,KAAKe,CAAG;AAC3C,QAAIf,GAAK;AACP,UAAIK,IAAOL,EAAI,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM,mBAAmB,GAAG,GAC3DsE,IAAmB,KAAK,MAAM,MAAM,aAAa,KAAKjE,CAAI,GAC1DkE,IAA0B,KAAK,MAAM,MAAM,kBAAkB,KAAKlE,CAAI,KAAK,KAAK,MAAM,MAAM,gBAAgB,KAAKA,CAAI;AAC3H,aAAIiE,KAAoBC,MACtBlE,IAAOA,EAAK,UAAU,GAAGA,EAAK,SAAS,CAAC,IAEnC,EACL,MAAM,YACN,KAAKL,EAAI,CAAC,GACV,MAAAK,EACF;IACF;EACF;EAEA,GAAGU,GAAoC;AACrC,QAAMf,IAAM,KAAK,MAAM,OAAO,GAAG,KAAKe,CAAG;AACzC,QAAIf,EACF,QAAO,EACL,MAAM,MACN,KAAKA,EAAI,CAAC,EACZ;EAEJ;EAEA,IAAIe,GAAqC;AACvC,QAAMf,IAAM,KAAK,MAAM,OAAO,IAAI,KAAKe,CAAG;AAC1C,QAAIf,EACF,QAAO,EACL,MAAM,OACN,KAAKA,EAAI,CAAC,GACV,MAAMA,EAAI,CAAC,GACX,QAAQ,KAAK,MAAM,aAAaA,EAAI,CAAC,CAAC,EACxC;EAEJ;EAEA,SAASe,GAAsC;AAC7C,QAAMf,IAAM,KAAK,MAAM,OAAO,SAAS,KAAKe,CAAG;AAC/C,QAAIf,GAAK;AACP,UAAIK,GAAM3B;AACV,aAAIsB,EAAI,CAAC,MAAM,OACbK,IAAOL,EAAI,CAAC,GACZtB,IAAO,YAAY2B,MAEnBA,IAAOL,EAAI,CAAC,GACZtB,IAAO2B,IAGF,EACL,MAAM,QACN,KAAKL,EAAI,CAAC,GACV,MAAAK,GACA,MAAA3B,GACA,QAAQ,CACN,EACE,MAAM,QACN,KAAK2B,GACL,MAAAA,EACF,CACF,EACF;IACF;EACF;EAEA,IAAIU,GAAsC;;AACxC,QAAIf;AACJ,QAAIA,IAAM,KAAK,MAAM,OAAO,IAAI,KAAKe,CAAG,GAAG;AACzC,UAAIV,GAAM3B;AACV,UAAIsB,EAAI,CAAC,MAAM,IACbK,KAAOL,EAAI,CAAC,GACZtB,IAAO,YAAY2B;WACd;AAEL,YAAImE;AACJ;AACEA,cAAcxE,EAAI,CAAC,GACnBA,EAAI,CAAC,MAAIyE,MAAA,KAAK,MAAM,OAAO,WAAW,KAAKzE,EAAI,CAAC,CAAC,MAAxC,gBAAAyE,IAA4C,OAAM;eACpDD,MAAgBxE,EAAI,CAAC;AAC9BK,YAAOL,EAAI,CAAC,GACRA,EAAI,CAAC,MAAM,SACbtB,IAAO,YAAYsB,EAAI,CAAC,IAExBtB,IAAOsB,EAAI,CAAC;MAEhB;AACA,aAAO,EACL,MAAM,QACN,KAAKA,EAAI,CAAC,GACV,MAAAK,GACA,MAAA3B,GACA,QAAQ,CACN,EACE,MAAM,QACN,KAAK2B,GACL,MAAAA,EACF,CACF,EACF;IACF;EACF;EAEA,WAAWU,GAAsC;AAC/C,QAAMf,IAAM,KAAK,MAAM,OAAO,KAAK,KAAKe,CAAG;AAC3C,QAAIf,GAAK;AACP,UAAMd,IAAU,KAAK,MAAM,MAAM;AACjC,aAAO,EACL,MAAM,QACN,KAAKc,EAAI,CAAC,GACV,MAAMA,EAAI,CAAC,GACX,SAAAd,EACF;IACF;EACF;AACF;ACn2BO,IAAMwF,IAAN,MAAMC,EAAuD;EAYlE,YAAY7D,GAAuD;AAXnE;AACA;AACA;AAMQ;AACA;AAIN,SAAK,SAAS,CAAC,GACf,KAAK,OAAO,QAAQ,uBAAO,OAAO,IAAI,GACtC,KAAK,UAAUA,KAAWjH,GAC1B,KAAK,QAAQ,YAAY,KAAK,QAAQ,aAAa,IAAIgH,KACvD,KAAK,YAAY,KAAK,QAAQ,WAC9B,KAAK,UAAU,UAAU,KAAK,SAC9B,KAAK,UAAU,QAAQ,MACvB,KAAK,cAAc,CAAC,GACpB,KAAK,QAAQ,EACX,QAAQ,OACR,YAAY,OACZ,KAAK,KACP;AAEA,QAAMV,IAAQ,EACZ,OAAA1F,GACA,OAAO0D,EAAM,QACb,QAAQC,EAAO,OACjB;AAEI,SAAK,QAAQ,YACf+B,EAAM,QAAQhC,EAAM,UACpBgC,EAAM,SAAS/B,EAAO,YACb,KAAK,QAAQ,QACtB+B,EAAM,QAAQhC,EAAM,KAChB,KAAK,QAAQ,SACfgC,EAAM,SAAS/B,EAAO,SAEtB+B,EAAM,SAAS/B,EAAO,MAG1B,KAAK,UAAU,QAAQ+B;EACzB;EAKA,WAAW,QAAQ;AACjB,WAAO,EACL,OAAAhC,GACA,QAAAC,EACF;EACF;EAKA,OAAO,IAAoD2C,GAAaD,GAAuD;AAE7H,WADc,IAAI6D,EAAqC7D,CAAO,EACjD,IAAIC,CAAG;EACtB;EAKA,OAAO,UAA0DA,GAAaD,GAAuD;AAEnI,WADc,IAAI6D,EAAqC7D,CAAO,EACjD,aAAaC,CAAG;EAC/B;EAKA,IAAIA,GAAa;AACfA,QAAMA,EAAI,QAAQtG,EAAM,gBAAgB;CAAI,GAE5C,KAAK,YAAYsG,GAAK,KAAK,MAAM;AAEjC,aAAS1B,IAAI,GAAGA,IAAI,KAAK,YAAY,QAAQA,KAAK;AAChD,UAAMuF,IAAO,KAAK,YAAYvF,CAAC;AAC/B,WAAK,aAAauF,EAAK,KAAKA,EAAK,MAAM;IACzC;AACA,WAAA,KAAK,cAAc,CAAC,GAEb,KAAK;EACd;EAOA,YAAY7D,GAAaG,IAAkB,CAAC,GAAG2D,IAAuB,OAAO;;AAK3E,SAJI,KAAK,QAAQ,aACf9D,IAAMA,EAAI,QAAQtG,EAAM,eAAe,MAAM,EAAE,QAAQA,EAAM,WAAW,EAAE,IAGrEsG,KAAK;AACV,UAAIT;AAEJ,WAAI,MAAAmE,MAAA,KAAK,QAAQ,eAAb,gBAAAA,IAAyB,UAAzB,mBAAgC,KAAMK,QACpCxE,IAAQwE,EAAa,KAAK,EAAE,OAAO,KAAK,GAAG/D,GAAKG,CAAM,MACxDH,IAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,GACpCY,EAAO,KAAKZ,CAAK,GACV,QAEF,OAEP;AAIF,UAAIA,IAAQ,KAAK,UAAU,MAAMS,CAAG,GAAG;AACrCA,YAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM;AACpC,YAAMkB,IAAYN,EAAO,GAAG,EAAE;AAC1BZ,UAAM,IAAI,WAAW,KAAKkB,MAAc,SAG1CA,EAAU,OAAO;IAEjBN,EAAO,KAAKZ,CAAK;AAEnB;MACF;AAGA,UAAIA,IAAQ,KAAK,UAAU,KAAKS,CAAG,GAAG;AACpCA,YAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM;AACpC,YAAMkB,IAAYN,EAAO,GAAG,EAAE;AAE1BM,gCAAW,UAAS,gBAAeA,uBAAW,UAAS,UACzDA,EAAU,OAAO;IAAOlB,EAAM,KAC9BkB,EAAU,QAAQ;IAAOlB,EAAM,MAC/B,KAAK,YAAY,GAAG,EAAE,EAAG,MAAMkB,EAAU,QAEzCN,EAAO,KAAKZ,CAAK;AAEnB;MACF;AAGA,UAAIA,IAAQ,KAAK,UAAU,OAAOS,CAAG,GAAG;AACtCA,YAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,GACpCY,EAAO,KAAKZ,CAAK;AACjB;MACF;AAGA,UAAIA,IAAQ,KAAK,UAAU,QAAQS,CAAG,GAAG;AACvCA,YAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,GACpCY,EAAO,KAAKZ,CAAK;AACjB;MACF;AAGA,UAAIA,IAAQ,KAAK,UAAU,GAAGS,CAAG,GAAG;AAClCA,YAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,GACpCY,EAAO,KAAKZ,CAAK;AACjB;MACF;AAGA,UAAIA,IAAQ,KAAK,UAAU,WAAWS,CAAG,GAAG;AAC1CA,YAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,GACpCY,EAAO,KAAKZ,CAAK;AACjB;MACF;AAGA,UAAIA,IAAQ,KAAK,UAAU,KAAKS,CAAG,GAAG;AACpCA,YAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,GACpCY,EAAO,KAAKZ,CAAK;AACjB;MACF;AAGA,UAAIA,IAAQ,KAAK,UAAU,KAAKS,CAAG,GAAG;AACpCA,YAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,GACpCY,EAAO,KAAKZ,CAAK;AACjB;MACF;AAGA,UAAIA,IAAQ,KAAK,UAAU,IAAIS,CAAG,GAAG;AACnCA,YAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM;AACpC,YAAMkB,IAAYN,EAAO,GAAG,EAAE;AAC1BM,gCAAW,UAAS,gBAAeA,uBAAW,UAAS,UACzDA,EAAU,OAAO;IAAOlB,EAAM,KAC9BkB,EAAU,QAAQ;IAAOlB,EAAM,KAC/B,KAAK,YAAY,GAAG,EAAE,EAAG,MAAMkB,EAAU,QAC/B,KAAK,OAAO,MAAMlB,EAAM,GAAG,MACrC,KAAK,OAAO,MAAMA,EAAM,GAAG,IAAI,EAC7B,MAAMA,EAAM,MACZ,OAAOA,EAAM,MACf;AAEF;MACF;AAGA,UAAIA,IAAQ,KAAK,UAAU,MAAMS,CAAG,GAAG;AACrCA,YAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,GACpCY,EAAO,KAAKZ,CAAK;AACjB;MACF;AAGA,UAAIA,IAAQ,KAAK,UAAU,SAASS,CAAG,GAAG;AACxCA,YAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,GACpCY,EAAO,KAAKZ,CAAK;AACjB;MACF;AAIA,UAAIyE,IAAShE;AACb,WAAI,UAAK,QAAQ,eAAb,mBAAyB,YAAY;AACvC,YAAIiE,IAAa,IAAA,GACXC,IAAUlE,EAAI,MAAM,CAAC,GACvBmE;AACJ,aAAK,QAAQ,WAAW,WAAW,QAASC,OAAkB;AAC5DD,cAAYC,EAAc,KAAK,EAAE,OAAO,KAAK,GAAGF,CAAO,GACnD,OAAOC,KAAc,YAAYA,KAAa,MAChDF,IAAa,KAAK,IAAIA,GAAYE,CAAS;QAE/C,CAAC,GACGF,IAAa,IAAA,KAAYA,KAAc,MACzCD,IAAShE,EAAI,UAAU,GAAGiE,IAAa,CAAC;MAE5C;AACA,UAAI,KAAK,MAAM,QAAQ1E,IAAQ,KAAK,UAAU,UAAUyE,CAAM,IAAI;AAChE,YAAMvD,IAAYN,EAAO,GAAG,EAAE;AAC1B2D,cAAwBrD,uBAAW,UAAS,eAC9CA,EAAU,OAAO;IAAOlB,EAAM,KAC9BkB,EAAU,QAAQ;IAAOlB,EAAM,MAC/B,KAAK,YAAY,IAAI,GACrB,KAAK,YAAY,GAAG,EAAE,EAAG,MAAMkB,EAAU,QAEzCN,EAAO,KAAKZ,CAAK,GAEnBuE,IAAuBE,EAAO,WAAWhE,EAAI,QAC7CA,IAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM;AACpC;MACF;AAGA,UAAIA,IAAQ,KAAK,UAAU,KAAKS,CAAG,GAAG;AACpCA,YAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM;AACpC,YAAMkB,IAAYN,EAAO,GAAG,EAAE;AAC1BM,gCAAW,UAAS,UACtBA,EAAU,OAAO;IAAOlB,EAAM,KAC9BkB,EAAU,QAAQ;IAAOlB,EAAM,MAC/B,KAAK,YAAY,IAAI,GACrB,KAAK,YAAY,GAAG,EAAE,EAAG,MAAMkB,EAAU,QAEzCN,EAAO,KAAKZ,CAAK;AAEnB;MACF;AAEA,UAAIS,GAAK;AACP,YAAMqE,IAAS,4BAA4BrE,EAAI,WAAW,CAAC;AAC3D,YAAI,KAAK,QAAQ,QAAQ;AACvB,kBAAQ,MAAMqE,CAAM;AACpB;QACF,MACE,OAAM,IAAI,MAAMA,CAAM;MAE1B;IACF;AAEA,WAAA,KAAK,MAAM,MAAM,MACVlE;EACT;EAEA,OAAOH,GAAaG,IAAkB,CAAC,GAAG;AACxC,WAAA,KAAK,YAAY,KAAK,EAAE,KAAAH,GAAK,QAAAG,EAAO,CAAC,GAC9BA;EACT;EAKA,aAAaH,GAAaG,IAAkB,CAAC,GAAY;;AAEvD,QAAI2C,IAAY9C,GACZhC,IAAgC;AAGpC,QAAI,KAAK,OAAO,OAAO;AACrB,UAAM4E,IAAQ,OAAO,KAAK,KAAK,OAAO,KAAK;AAC3C,UAAIA,EAAM,SAAS,EACjB,SAAQ5E,IAAQ,KAAK,UAAU,MAAM,OAAO,cAAc,KAAK8E,CAAS,MAAM,OACxEF,GAAM,SAAS5E,EAAM,CAAC,EAAE,MAAMA,EAAM,CAAC,EAAE,YAAY,GAAG,IAAI,GAAG,EAAE,CAAC,MAClE8E,IAAYA,EAAU,MAAM,GAAG9E,EAAM,KAAK,IACtC,MAAM,IAAI,OAAOA,EAAM,CAAC,EAAE,SAAS,CAAC,IAAI,MACxC8E,EAAU,MAAM,KAAK,UAAU,MAAM,OAAO,cAAc,SAAS;IAI/E;AAGA,YAAQ9E,IAAQ,KAAK,UAAU,MAAM,OAAO,eAAe,KAAK8E,CAAS,MAAM,OAC7EA,KAAYA,EAAU,MAAM,GAAG9E,EAAM,KAAK,IAAI,OAAO8E,EAAU,MAAM,KAAK,UAAU,MAAM,OAAO,eAAe,SAAS;AAI3H,YAAQ9E,IAAQ,KAAK,UAAU,MAAM,OAAO,UAAU,KAAK8E,CAAS,MAAM,OACxEA,KAAYA,EAAU,MAAM,GAAG9E,EAAM,KAAK,IAAI,MAAM,IAAI,OAAOA,EAAM,CAAC,EAAE,SAAS,CAAC,IAAI,MAAM8E,EAAU,MAAM,KAAK,UAAU,MAAM,OAAO,UAAU,SAAS;AAG7J,QAAIwB,IAAe,OACfvB,IAAW;AACf,WAAO/C,KAAK;AACLsE,YACHvB,IAAW,KAEbuB,IAAe;AAEf,UAAI/E;AAGJ,WAAI,MAAAmE,MAAA,KAAK,QAAQ,eAAb,gBAAAA,IAAyB,WAAzB,mBAAiC,KAAMK,QACrCxE,IAAQwE,EAAa,KAAK,EAAE,OAAO,KAAK,GAAG/D,GAAKG,CAAM,MACxDH,IAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,GACpCY,EAAO,KAAKZ,CAAK,GACV,QAEF,OAEP;AAIF,UAAIA,IAAQ,KAAK,UAAU,OAAOS,CAAG,GAAG;AACtCA,YAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,GACpCY,EAAO,KAAKZ,CAAK;AACjB;MACF;AAGA,UAAIA,IAAQ,KAAK,UAAU,IAAIS,CAAG,GAAG;AACnCA,YAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,GACpCY,EAAO,KAAKZ,CAAK;AACjB;MACF;AAGA,UAAIA,IAAQ,KAAK,UAAU,KAAKS,CAAG,GAAG;AACpCA,YAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,GACpCY,EAAO,KAAKZ,CAAK;AACjB;MACF;AAGA,UAAIA,IAAQ,KAAK,UAAU,QAAQS,GAAK,KAAK,OAAO,KAAK,GAAG;AAC1DA,YAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM;AACpC,YAAMkB,IAAYN,EAAO,GAAG,EAAE;AAC1BZ,UAAM,SAAS,WAAUkB,uBAAW,UAAS,UAC/CA,EAAU,OAAOlB,EAAM,KACvBkB,EAAU,QAAQlB,EAAM,QAExBY,EAAO,KAAKZ,CAAK;AAEnB;MACF;AAGA,UAAIA,IAAQ,KAAK,UAAU,SAASS,GAAK8C,GAAWC,CAAQ,GAAG;AAC7D/C,YAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,GACpCY,EAAO,KAAKZ,CAAK;AACjB;MACF;AAGA,UAAIA,IAAQ,KAAK,UAAU,SAASS,CAAG,GAAG;AACxCA,YAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,GACpCY,EAAO,KAAKZ,CAAK;AACjB;MACF;AAGA,UAAIA,IAAQ,KAAK,UAAU,GAAGS,CAAG,GAAG;AAClCA,YAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,GACpCY,EAAO,KAAKZ,CAAK;AACjB;MACF;AAGA,UAAIA,IAAQ,KAAK,UAAU,IAAIS,CAAG,GAAG;AACnCA,YAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,GACpCY,EAAO,KAAKZ,CAAK;AACjB;MACF;AAGA,UAAIA,IAAQ,KAAK,UAAU,SAASS,CAAG,GAAG;AACxCA,YAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,GACpCY,EAAO,KAAKZ,CAAK;AACjB;MACF;AAGA,UAAI,CAAC,KAAK,MAAM,WAAWA,IAAQ,KAAK,UAAU,IAAIS,CAAG,IAAI;AAC3DA,YAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,GACpCY,EAAO,KAAKZ,CAAK;AACjB;MACF;AAIA,UAAIyE,IAAShE;AACb,WAAI,UAAK,QAAQ,eAAb,mBAAyB,aAAa;AACxC,YAAIiE,IAAa,IAAA,GACXC,IAAUlE,EAAI,MAAM,CAAC,GACvBmE;AACJ,aAAK,QAAQ,WAAW,YAAY,QAASC,OAAkB;AAC7DD,cAAYC,EAAc,KAAK,EAAE,OAAO,KAAK,GAAGF,CAAO,GACnD,OAAOC,KAAc,YAAYA,KAAa,MAChDF,IAAa,KAAK,IAAIA,GAAYE,CAAS;QAE/C,CAAC,GACGF,IAAa,IAAA,KAAYA,KAAc,MACzCD,IAAShE,EAAI,UAAU,GAAGiE,IAAa,CAAC;MAE5C;AACA,UAAI1E,IAAQ,KAAK,UAAU,WAAWyE,CAAM,GAAG;AAC7ChE,YAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,GAChCA,EAAM,IAAI,MAAM,EAAE,MAAM,QAC1BwD,IAAWxD,EAAM,IAAI,MAAM,EAAE,IAE/B+E,IAAe;AACf,YAAM7D,IAAYN,EAAO,GAAG,EAAE;AAC1BM,gCAAW,UAAS,UACtBA,EAAU,OAAOlB,EAAM,KACvBkB,EAAU,QAAQlB,EAAM,QAExBY,EAAO,KAAKZ,CAAK;AAEnB;MACF;AAEA,UAAIS,GAAK;AACP,YAAMqE,IAAS,4BAA4BrE,EAAI,WAAW,CAAC;AAC3D,YAAI,KAAK,QAAQ,QAAQ;AACvB,kBAAQ,MAAMqE,CAAM;AACpB;QACF,MACE,OAAM,IAAI,MAAMA,CAAM;MAE1B;IACF;AAEA,WAAOlE;EACT;AACF;ACxcO,IAAMoE,IAAN,MAAgE;EAGrE,YAAYxE,GAAuD;AAFnE;AACA;AAEE,SAAK,UAAUA,KAAWjH;EAC5B;EAEA,MAAMyG,GAAqC;AACzC,WAAO;EACT;EAEA,KAAK,EAAE,MAAAD,GAAM,MAAAkF,GAAM,SAAArG,EAAQ,GAAgC;;AACzD,QAAMsG,KAAcD,OAAAA,KAAQ,IAAI,MAAM9K,EAAM,aAAa,MAArC8K,gBAAAA,IAAyC,IAEvDE,IAAOpF,EAAK,QAAQ5F,EAAM,eAAe,EAAE,IAAI;;AAErD,WAAK+K,IAME,gCACHrJ,EAAOqJ,CAAU,IACjB,QACCtG,IAAUuG,IAAOtJ,EAAOsJ,GAAM,IAAI,KACnC;IATK,iBACFvG,IAAUuG,IAAOtJ,EAAOsJ,GAAM,IAAI,KACnC;;EAQR;EAEA,WAAW,EAAE,QAAAvE,EAAO,GAAsC;AAExD,WAAO;EADM,KAAK,OAAO,MAAMA,CAAM,CACT;;EAC9B;EAEA,KAAK,EAAE,MAAAb,EAAK,GAA6C;AACvD,WAAOA;EACT;EAEA,QAAQ,EAAE,QAAAa,GAAQ,OAAAwE,EAAM,GAAmC;AACzD,WAAO,KAAKA,CAAK,IAAI,KAAK,OAAO,YAAYxE,CAAM,CAAC,MAAMwE,CAAK;;EACjE;EAEA,GAAGpF,GAAkC;AACnC,WAAO;;EACT;EAEA,KAAKA,GAAoC;AACvC,QAAMqF,IAAUrF,EAAM,SAChBsF,IAAQtF,EAAM,OAEhBuF,IAAO;AACX,aAASC,IAAI,GAAGA,IAAIxF,EAAM,MAAM,QAAQwF,KAAK;AAC3C,UAAM1C,IAAO9C,EAAM,MAAMwF,CAAC;AAC1BD,WAAQ,KAAK,SAASzC,CAAI;IAC5B;AAEA,QAAM2C,IAAOJ,IAAU,OAAO,MACxBK,IAAaL,KAAWC,MAAU,IAAM,aAAaA,IAAQ,MAAO;AAC1E,WAAO,MAAMG,IAAOC,IAAY;IAAQH,IAAO,OAAOE,IAAO;;EAC/D;EAEA,SAAS3C,GAAuC;;AAC9C,QAAI6C,IAAW;AACf,QAAI7C,EAAK,MAAM;AACb,UAAM8C,IAAW,KAAK,SAAS,EAAE,SAAS,CAAC,CAAC9C,EAAK,QAAQ,CAAC;AACtDA,QAAK,UACHA,MAAAA,EAAK,OAAO,CAAC,MAAbA,gBAAAA,IAAgB,UAAS,eAC3BA,EAAK,OAAO,CAAC,EAAE,OAAO8C,IAAW,MAAM9C,EAAK,OAAO,CAAC,EAAE,MAClDA,EAAK,OAAO,CAAC,EAAE,UAAUA,EAAK,OAAO,CAAC,EAAE,OAAO,SAAS,KAAKA,EAAK,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,SAAS,WACjGA,EAAK,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,OAAO8C,IAAW,MAAM/J,EAAOiH,EAAK,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,IAAI,GACrFA,EAAK,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,UAAU,SAGrCA,EAAK,OAAO,QAAQ,EAClB,MAAM,QACN,KAAK8C,IAAW,KAChB,MAAMA,IAAW,KACjB,SAAS,KACX,CAAC,IAGHD,KAAYC,IAAW;IAE3B;AAEA,WAAAD,KAAY,KAAK,OAAO,MAAM7C,EAAK,QAAQ,CAAC,CAACA,EAAK,KAAK,GAEhD,OAAO6C,CAAQ;;EACxB;EAEA,SAAS,EAAE,SAAAE,EAAQ,GAAoC;AACrD,WAAO,aACFA,IAAU,gBAAgB,MAC3B;EACN;EAEA,UAAU,EAAE,QAAAjF,EAAO,GAAqC;AACtD,WAAO,MAAM,KAAK,OAAO,YAAYA,CAAM,CAAC;;EAC9C;EAEA,MAAMZ,GAAqC;AACzC,QAAI8F,IAAS,IAGT9C,IAAO;AACX,aAASwC,IAAI,GAAGA,IAAIxF,EAAM,OAAO,QAAQwF,IACvCxC,MAAQ,KAAK,UAAUhD,EAAM,OAAOwF,CAAC,CAAC;AAExCM,SAAU,KAAK,SAAS,EAAE,MAAM9C,EAAqB,CAAC;AAEtD,QAAIuC,IAAO;AACX,aAASC,IAAI,GAAGA,IAAIxF,EAAM,KAAK,QAAQwF,KAAK;AAC1C,UAAMhH,IAAMwB,EAAM,KAAKwF,CAAC;AAExBxC,UAAO;AACP,eAAS+C,IAAI,GAAGA,IAAIvH,EAAI,QAAQuH,IAC9B/C,MAAQ,KAAK,UAAUxE,EAAIuH,CAAC,CAAC;AAG/BR,WAAQ,KAAK,SAAS,EAAE,MAAMvC,EAAqB,CAAC;IACtD;AACA,WAAIuC,MAAMA,IAAO,UAAUA,CAAI,aAExB;;IAEHO,IACA;IACAP,IACA;;EACN;EAEA,SAAS,EAAE,MAAAxF,EAAK,GAAkD;AAChE,WAAO;EAASA,CAAI;;EACtB;EAEA,UAAUC,GAAyC;AACjD,QAAMgG,IAAU,KAAK,OAAO,YAAYhG,EAAM,MAAM,GAC9CyF,IAAOzF,EAAM,SAAS,OAAO;AAInC,YAHYA,EAAM,QACd,IAAIyF,CAAI,WAAWzF,EAAM,KAAK,OAC9B,IAAIyF,CAAI,OACCO,IAAU,KAAKP,CAAI;;EAClC;EAKA,OAAO,EAAE,QAAA7E,EAAO,GAAkC;AAChD,WAAO,WAAW,KAAK,OAAO,YAAYA,CAAM,CAAC;EACnD;EAEA,GAAG,EAAE,QAAAA,EAAO,GAA8B;AACxC,WAAO,OAAO,KAAK,OAAO,YAAYA,CAAM,CAAC;EAC/C;EAEA,SAAS,EAAE,MAAAb,EAAK,GAAoC;AAClD,WAAO,SAASlE,EAAOkE,GAAM,IAAI,CAAC;EACpC;EAEA,GAAGC,GAAkC;AACnC,WAAO;EACT;EAEA,IAAI,EAAE,QAAAY,EAAO,GAA+B;AAC1C,WAAO,QAAQ,KAAK,OAAO,YAAYA,CAAM,CAAC;EAChD;EAEA,KAAK,EAAE,MAAAxC,GAAM,OAAA0B,GAAO,QAAAc,EAAO,GAAgC;AACzD,QAAMb,IAAO,KAAK,OAAO,YAAYa,CAAM,GACrCqF,IAAY9H,EAASC,CAAI;AAC/B,QAAI6H,MAAc,KAChB,QAAOlG;AAET3B,QAAO6H;AACP,QAAIC,IAAM,cAAc9H,IAAO;AAC/B,WAAI0B,MACFoG,KAAO,aAAcrK,EAAOiE,CAAK,IAAK,MAExCoG,KAAO,MAAMnG,IAAO,QACbmG;EACT;EAEA,MAAM,EAAE,MAAA9H,GAAM,OAAA0B,GAAO,MAAAC,GAAM,QAAAa,EAAO,GAAiC;AAC7DA,UACFb,IAAO,KAAK,OAAO,YAAYa,GAAQ,KAAK,OAAO,YAAY;AAEjE,QAAMqF,IAAY9H,EAASC,CAAI;AAC/B,QAAI6H,MAAc,KAChB,QAAOpK,EAAOkE,CAAI;AAEpB3B,QAAO6H;AAEP,QAAIC,IAAM,aAAa9H,CAAI,UAAU2B,CAAI;AACzC,WAAID,MACFoG,KAAO,WAAWrK,EAAOiE,CAAK,CAAC,MAEjCoG,KAAO,KACAA;EACT;EAEA,KAAKlG,GAAoD;AACvD,WAAO,YAAYA,KAASA,EAAM,SAC9B,KAAK,OAAO,YAAYA,EAAM,MAAM,IACnC,aAAaA,KAASA,EAAM,UAAUA,EAAM,OAAyBnE,EAAOmE,EAAM,IAAI;EAC7F;AACF;ACpNO,IAAMmG,IAAN,MAA6C;EAElD,OAAO,EAAE,MAAApG,EAAK,GAAkC;AAC9C,WAAOA;EACT;EAEA,GAAG,EAAE,MAAAA,EAAK,GAA8B;AACtC,WAAOA;EACT;EAEA,SAAS,EAAE,MAAAA,EAAK,GAAoC;AAClD,WAAOA;EACT;EAEA,IAAI,EAAE,MAAAA,EAAK,GAA+B;AACxC,WAAOA;EACT;EAEA,KAAK,EAAE,MAAAA,EAAK,GAA6C;AACvD,WAAOA;EACT;EAEA,KAAK,EAAE,MAAAA,EAAK,GAA6D;AACvE,WAAOA;EACT;EAEA,KAAK,EAAE,MAAAA,EAAK,GAAgC;AAC1C,WAAO,KAAKA;EACd;EAEA,MAAM,EAAE,MAAAA,EAAK,GAAiC;AAC5C,WAAO,KAAKA;EACd;EAEA,KAAqB;AACnB,WAAO;EACT;AACF;AClCO,IAAMqG,IAAN,MAAMC,GAAwD;EAInE,YAAY7F,GAAuD;AAHnE;AACA;AACA;AAEE,SAAK,UAAUA,KAAWjH,GAC1B,KAAK,QAAQ,WAAW,KAAK,QAAQ,YAAY,IAAIyL,KACrD,KAAK,WAAW,KAAK,QAAQ,UAC7B,KAAK,SAAS,UAAU,KAAK,SAC7B,KAAK,SAAS,SAAS,MACvB,KAAK,eAAe,IAAImB;EAC1B;EAKA,OAAO,MAAsDvF,GAAiBJ,GAAuD;AAEnI,WADe,IAAI6F,GAAsC7F,CAAO,EAClD,MAAMI,CAAM;EAC5B;EAKA,OAAO,YAA4DA,GAAiBJ,GAAuD;AAEzI,WADe,IAAI6F,GAAsC7F,CAAO,EAClD,YAAYI,CAAM;EAClC;EAKA,MAAMA,GAAiBK,IAAM,MAAoB;;AAC/C,QAAIiF,IAAM;AAEV,aAASnH,IAAI,GAAGA,IAAI6B,EAAO,QAAQ7B,KAAK;AACtC,UAAMuH,IAAW1F,EAAO7B,CAAC;AAGzB,WAAI,MAAAoF,MAAA,KAAK,QAAQ,eAAb,gBAAAA,IAAyB,cAAzB,mBAAqCmC,EAAS,OAAO;AACvD,YAAMC,IAAeD,GACfE,IAAM,KAAK,QAAQ,WAAW,UAAUD,EAAa,IAAI,EAAE,KAAK,EAAE,QAAQ,KAAK,GAAGA,CAAY;AACpG,YAAIC,MAAQ,SAAS,CAAC,CAAC,SAAS,MAAM,WAAW,QAAQ,SAAS,cAAc,QAAQ,QAAQ,aAAa,MAAM,EAAE,SAASD,EAAa,IAAI,GAAG;AAChJL,eAAOM,KAAO;AACd;QACF;MACF;AAEA,UAAMxG,IAAQsG;AAEd,cAAQtG,EAAM,MAAM;QAClB,KAAK,SAAS;AACZkG,eAAO,KAAK,SAAS,MAAMlG,CAAK;AAChC;QACF;QACA,KAAK,MAAM;AACTkG,eAAO,KAAK,SAAS,GAAGlG,CAAK;AAC7B;QACF;QACA,KAAK,WAAW;AACdkG,eAAO,KAAK,SAAS,QAAQlG,CAAK;AAClC;QACF;QACA,KAAK,QAAQ;AACXkG,eAAO,KAAK,SAAS,KAAKlG,CAAK;AAC/B;QACF;QACA,KAAK,SAAS;AACZkG,eAAO,KAAK,SAAS,MAAMlG,CAAK;AAChC;QACF;QACA,KAAK,cAAc;AACjBkG,eAAO,KAAK,SAAS,WAAWlG,CAAK;AACrC;QACF;QACA,KAAK,QAAQ;AACXkG,eAAO,KAAK,SAAS,KAAKlG,CAAK;AAC/B;QACF;QACA,KAAK,QAAQ;AACXkG,eAAO,KAAK,SAAS,KAAKlG,CAAK;AAC/B;QACF;QACA,KAAK,aAAa;AAChBkG,eAAO,KAAK,SAAS,UAAUlG,CAAK;AACpC;QACF;QACA,KAAK,QAAQ;AACX,cAAIyG,IAAYzG,GACZuF,IAAO,KAAK,SAAS,KAAKkB,CAAS;AACvC,iBAAO1H,IAAI,IAAI6B,EAAO,UAAUA,EAAO7B,IAAI,CAAC,EAAE,SAAS,SACrD0H,KAAY7F,EAAO,EAAE7B,CAAC,GACtBwG,KAAS;IAAO,KAAK,SAAS,KAAKkB,CAAS;AAE1CxF,cACFiF,KAAO,KAAK,SAAS,UAAU,EAC7B,MAAM,aACN,KAAKX,GACL,MAAMA,GACN,QAAQ,CAAC,EAAE,MAAM,QAAQ,KAAKA,GAAM,MAAMA,GAAM,SAAS,KAAK,CAAC,EACjE,CAAC,IAEDW,KAAOX;AAET;QACF;QAEA,SAAS;AACP,cAAMT,IAAS,iBAAiB9E,EAAM,OAAO;AAC7C,cAAI,KAAK,QAAQ,OACf,QAAA,QAAQ,MAAM8E,CAAM,GACb;AAEP,gBAAM,IAAI,MAAMA,CAAM;QAE1B;MACF;IACF;AAEA,WAAOoB;EACT;EAKA,YAAYtF,GAAiB8F,IAAoF,KAAK,UAAwB;;AAC5I,QAAIR,IAAM;AAEV,aAASnH,IAAI,GAAGA,IAAI6B,EAAO,QAAQ7B,KAAK;AACtC,UAAMuH,IAAW1F,EAAO7B,CAAC;AAGzB,WAAI,MAAAoF,MAAA,KAAK,QAAQ,eAAb,gBAAAA,IAAyB,cAAzB,mBAAqCmC,EAAS,OAAO;AACvD,YAAME,IAAM,KAAK,QAAQ,WAAW,UAAUF,EAAS,IAAI,EAAE,KAAK,EAAE,QAAQ,KAAK,GAAGA,CAAQ;AAC5F,YAAIE,MAAQ,SAAS,CAAC,CAAC,UAAU,QAAQ,QAAQ,SAAS,UAAU,MAAM,YAAY,MAAM,OAAO,MAAM,EAAE,SAASF,EAAS,IAAI,GAAG;AAClIJ,eAAOM,KAAO;AACd;QACF;MACF;AAEA,UAAMxG,IAAQsG;AAEd,cAAQtG,EAAM,MAAM;QAClB,KAAK,UAAU;AACbkG,eAAOQ,EAAS,KAAK1G,CAAK;AAC1B;QACF;QACA,KAAK,QAAQ;AACXkG,eAAOQ,EAAS,KAAK1G,CAAK;AAC1B;QACF;QACA,KAAK,QAAQ;AACXkG,eAAOQ,EAAS,KAAK1G,CAAK;AAC1B;QACF;QACA,KAAK,SAAS;AACZkG,eAAOQ,EAAS,MAAM1G,CAAK;AAC3B;QACF;QACA,KAAK,UAAU;AACbkG,eAAOQ,EAAS,OAAO1G,CAAK;AAC5B;QACF;QACA,KAAK,MAAM;AACTkG,eAAOQ,EAAS,GAAG1G,CAAK;AACxB;QACF;QACA,KAAK,YAAY;AACfkG,eAAOQ,EAAS,SAAS1G,CAAK;AAC9B;QACF;QACA,KAAK,MAAM;AACTkG,eAAOQ,EAAS,GAAG1G,CAAK;AACxB;QACF;QACA,KAAK,OAAO;AACVkG,eAAOQ,EAAS,IAAI1G,CAAK;AACzB;QACF;QACA,KAAK,QAAQ;AACXkG,eAAOQ,EAAS,KAAK1G,CAAK;AAC1B;QACF;QACA,SAAS;AACP,cAAM8E,IAAS,iBAAiB9E,EAAM,OAAO;AAC7C,cAAI,KAAK,QAAQ,OACf,QAAA,QAAQ,MAAM8E,CAAM,GACb;AAEP,gBAAM,IAAI,MAAMA,CAAM;QAE1B;MACF;IACF;AACA,WAAOoB;EACT;AACF;;ACvMO,IAAMS,KAAN,WAA6D;EAIlE,YAAYnG,GAAuD;AAHnE;AACA;AAGE,SAAK,UAAUA,KAAWjH;EAC5B;EAWA,WAAWqN,GAAkB;AAC3B,WAAOA;EACT;EAKA,YAAYtL,GAAoB;AAC9B,WAAOA;EACT;EAKA,iBAAiBsF,GAA8B;AAC7C,WAAOA;EACT;EAKA,eAAe;AACb,WAAO,KAAK,QAAQwD,EAAO,MAAMA,EAAO;EAC1C;EAKA,gBAAgB;AACd,WAAO,KAAK,QAAQgC,EAAQ,QAAsCA,EAAQ;EAC5E;AACF,GAxCE,cARK,IAQE,oBAAmB,oBAAI,IAAI,CAChC,cACA,eACA,kBACF,CAAC,IAZI;ACUA,IAAMS,IAAN,MAA6D;EAclE,eAAeC,GAAuD;AAbtE,oCAAWxN,EAA2C;AACtD,mCAAU,KAAK;AAEf,iCAAQ,KAAK,cAAc,IAAI;AAC/B,uCAAc,KAAK,cAAc,KAAK;AAEtC,kCAAS8M;AACT,oCAAWpB;AACX,wCAAemB;AACf,iCAAQ/B;AACR,qCAAY7D;AACZ,iCAAQoG;AAGN,SAAK,IAAI,GAAGG,CAAI;EAClB;EAKA,WAAWlG,GAA8BmG,GAA2D;;AAClG,QAAIC,IAAyB,CAAC;AAC9B,aAAWhH,KAASY,EAElB,SADAoG,IAASA,EAAO,OAAOD,EAAS,KAAK,MAAM/G,CAAK,CAAC,GACzCA,EAAM,MAAM;MAClB,KAAK,SAAS;AACZ,YAAMiH,IAAajH;AACnB,iBAAWgD,KAAQiE,EAAW,OAC5BD,KAASA,EAAO,OAAO,KAAK,WAAWhE,EAAK,QAAQ+D,CAAQ,CAAC;AAE/D,iBAAWvI,KAAOyI,EAAW,KAC3B,UAAWjE,KAAQxE,EACjBwI,KAASA,EAAO,OAAO,KAAK,WAAWhE,EAAK,QAAQ+D,CAAQ,CAAC;AAGjE;MACF;MACA,KAAK,QAAQ;AACX,YAAMG,IAAYlH;AAClBgH,YAASA,EAAO,OAAO,KAAK,WAAWE,EAAU,OAAOH,CAAQ,CAAC;AACjE;MACF;MACA,SAAS;AACP,YAAMR,IAAevG;AACjB,gBAAAmE,MAAA,KAAK,SAAS,eAAd,gBAAAA,IAA0B,gBAA1B,mBAAwCoC,EAAa,SACvD,KAAK,SAAS,WAAW,YAAYA,EAAa,IAAI,EAAE,QAASY,OAAgB;AAC/E,cAAMvG,IAAS2F,EAAaY,CAAW,EAAE,KAAK,IAAA,CAAQ;AACtDH,cAASA,EAAO,OAAO,KAAK,WAAWpG,GAAQmG,CAAQ,CAAC;QAC1D,CAAC,IACQR,EAAa,WACtBS,IAASA,EAAO,OAAO,KAAK,WAAWT,EAAa,QAAQQ,CAAQ,CAAC;MAEzE;IACF;AAEF,WAAOC;EACT;EAEA,OAAOF,GAAuD;AAC5D,QAAMM,IAAwE,KAAK,SAAS,cAAc,EAAE,WAAW,CAAC,GAAG,aAAa,CAAC,EAAE;AAE3I,WAAAN,EAAK,QAASO,OAAS;AAErB,UAAMC,IAAO,EAAE,GAAGD,EAAK;AA4DvB,UAzDAC,EAAK,QAAQ,KAAK,SAAS,SAASA,EAAK,SAAS,OAG9CD,EAAK,eACPA,EAAK,WAAW,QAASE,OAAQ;AAC/B,YAAI,CAACA,EAAI,KACP,OAAM,IAAI,MAAM,yBAAyB;AAE3C,YAAI,cAAcA,GAAK;AACrB,cAAMC,IAAeJ,EAAW,UAAUG,EAAI,IAAI;AAC9CC,cAEFJ,EAAW,UAAUG,EAAI,IAAI,IAAI,YAAYT,GAAM;AACjD,gBAAIN,IAAMe,EAAI,SAAS,MAAM,MAAMT,CAAI;AACvC,mBAAIN,MAAQ,UACVA,IAAMgB,EAAa,MAAM,MAAMV,CAAI,IAE9BN;UACT,IAEAY,EAAW,UAAUG,EAAI,IAAI,IAAIA,EAAI;QAEzC;AACA,YAAI,eAAeA,GAAK;AACtB,cAAI,CAACA,EAAI,SAAUA,EAAI,UAAU,WAAWA,EAAI,UAAU,SACxD,OAAM,IAAI,MAAM,6CAA6C;AAE/D,cAAME,IAAWL,EAAWG,EAAI,KAAK;AACjCE,cACFA,EAAS,QAAQF,EAAI,SAAS,IAE9BH,EAAWG,EAAI,KAAK,IAAI,CAACA,EAAI,SAAS,GAEpCA,EAAI,UACFA,EAAI,UAAU,UACZH,EAAW,aACbA,EAAW,WAAW,KAAKG,EAAI,KAAK,IAEpCH,EAAW,aAAa,CAACG,EAAI,KAAK,IAE3BA,EAAI,UAAU,aACnBH,EAAW,cACbA,EAAW,YAAY,KAAKG,EAAI,KAAK,IAErCH,EAAW,cAAc,CAACG,EAAI,KAAK;QAI3C;AACI,yBAAiBA,KAAOA,EAAI,gBAC9BH,EAAW,YAAYG,EAAI,IAAI,IAAIA,EAAI;MAE3C,CAAC,GACDD,EAAK,aAAaF,IAIhBC,EAAK,UAAU;AACjB,YAAMX,IAAW,KAAK,SAAS,YAAY,IAAI1B,EAAwC,KAAK,QAAQ;AACpG,iBAAW0C,KAAQL,EAAK,UAAU;AAChC,cAAI,EAAEK,KAAQhB,GACZ,OAAM,IAAI,MAAM,aAAagB,CAAI,kBAAkB;AAErD,cAAI,CAAC,WAAW,QAAQ,EAAE,SAASA,CAAI,EAErC;AAEF,cAAMC,IAAeD,GACfE,IAAeP,EAAK,SAASM,CAAY,GACzCH,IAAed,EAASiB,CAAY;AAE1CjB,YAASiB,CAAY,IAAI,IAAIb,MAAoB;AAC/C,gBAAIN,IAAMoB,EAAa,MAAMlB,GAAUI,CAAI;AAC3C,mBAAIN,MAAQ,UACVA,IAAMgB,EAAa,MAAMd,GAAUI,CAAI,IAEjCN,KAAO;UACjB;QACF;AACAc,UAAK,WAAWZ;MAClB;AACA,UAAIW,EAAK,WAAW;AAClB,YAAMQ,IAAY,KAAK,SAAS,aAAa,IAAItH,EAAyC,KAAK,QAAQ;AACvG,iBAAWmH,KAAQL,EAAK,WAAW;AACjC,cAAI,EAAEK,KAAQG,GACZ,OAAM,IAAI,MAAM,cAAcH,CAAI,kBAAkB;AAEtD,cAAI,CAAC,WAAW,SAAS,OAAO,EAAE,SAASA,CAAI,EAE7C;AAEF,cAAMI,IAAgBJ,GAChBK,IAAgBV,EAAK,UAAUS,CAAa,GAC5CE,IAAgBH,EAAUC,CAAa;AAG7CD,YAAUC,CAAa,IAAI,IAAIhB,MAAoB;AACjD,gBAAIN,IAAMuB,EAAc,MAAMF,GAAWf,CAAI;AAC7C,mBAAIN,MAAQ,UACVA,IAAMwB,EAAc,MAAMH,GAAWf,CAAI,IAEpCN;UACT;QACF;AACAc,UAAK,YAAYO;MACnB;AAGA,UAAIR,EAAK,OAAO;AACd,YAAMY,IAAQ,KAAK,SAAS,SAAS,IAAItB;AACzC,iBAAWe,KAAQL,EAAK,OAAO;AAC7B,cAAI,EAAEK,KAAQO,GACZ,OAAM,IAAI,MAAM,SAASP,CAAI,kBAAkB;AAEjD,cAAI,CAAC,WAAW,OAAO,EAAE,SAASA,CAAI,EAEpC;AAEF,cAAMQ,IAAYR,GACZS,IAAYd,EAAK,MAAMa,CAAS,GAChCE,IAAWH,EAAMC,CAAS;AAC5BvB,YAAO,iBAAiB,IAAIe,CAAI,IAElCO,EAAMC,CAAS,IAAKG,OAAiB;AACnC,gBAAI,KAAK,SAAS,MAChB,QAAO,QAAQ,QAAQF,EAAU,KAAKF,GAAOI,CAAG,CAAC,EAAE,KAAK7B,OAC/C4B,EAAS,KAAKH,GAAOzB,CAAG,CAChC;AAGH,gBAAMA,IAAM2B,EAAU,KAAKF,GAAOI,CAAG;AACrC,mBAAOD,EAAS,KAAKH,GAAOzB,CAAG;UACjC,IAGAyB,EAAMC,CAAS,IAAI,IAAIpB,MAAoB;AACzC,gBAAIN,IAAM2B,EAAU,MAAMF,GAAOnB,CAAI;AACrC,mBAAIN,MAAQ,UACVA,IAAM4B,EAAS,MAAMH,GAAOnB,CAAI,IAE3BN;UACT;QAEJ;AACAc,UAAK,QAAQW;MACf;AAGA,UAAIZ,EAAK,YAAY;AACnB,YAAMiB,IAAa,KAAK,SAAS,YAC3BC,IAAiBlB,EAAK;AAC5BC,UAAK,aAAa,SAAStH,GAAO;AAChC,cAAIgH,IAAyB,CAAC;AAC9B,iBAAAA,EAAO,KAAKuB,EAAe,KAAK,MAAMvI,CAAK,CAAC,GACxCsI,MACFtB,IAASA,EAAO,OAAOsB,EAAW,KAAK,MAAMtI,CAAK,CAAC,IAE9CgH;QACT;MACF;AAEA,WAAK,WAAW,EAAE,GAAG,KAAK,UAAU,GAAGM,EAAK;IAC9C,CAAC,GAEM;EACT;EAEA,WAAWzN,GAAkD;AAC3D,WAAA,KAAK,WAAW,EAAE,GAAG,KAAK,UAAU,GAAGA,EAAI,GACpC;EACT;EAEA,MAAM4G,GAAaD,GAAuD;AACxE,WAAO4D,EAAO,IAAI3D,GAAKD,KAAW,KAAK,QAAQ;EACjD;EAEA,OAAOI,GAAiBJ,GAAuD;AAC7E,WAAO4F,EAAQ,MAAoCxF,GAAQJ,KAAW,KAAK,QAAQ;EACrF;EAEQ,cAAcgI,GAAoB;AAmExC,WA3D+B,CAAC/H,GAAaD,MAAsE;AACjH,UAAMiI,IAAU,EAAE,GAAGjI,EAAQ,GACvB3G,IAAM,EAAE,GAAG,KAAK,UAAU,GAAG4O,EAAQ,GAErCC,IAAa,KAAK,QAAQ,CAAC,CAAC7O,EAAI,QAAQ,CAAC,CAACA,EAAI,KAAK;AAGzD,UAAI,KAAK,SAAS,UAAU,QAAQ4O,EAAQ,UAAU,MACpD,QAAOC,EAAW,IAAI,MAAM,oIAAoI,CAAC;AAInK,UAAI,OAAOjI,IAAQ,OAAeA,MAAQ,KACxC,QAAOiI,EAAW,IAAI,MAAM,gDAAgD,CAAC;AAE/E,UAAI,OAAOjI,KAAQ,SACjB,QAAOiI,EAAW,IAAI,MAAM,0CACxB,OAAO,UAAU,SAAS,KAAKjI,CAAG,IAAI,mBAAmB,CAAC;AAG5D5G,QAAI,UACNA,EAAI,MAAM,UAAUA,GACpBA,EAAI,MAAM,QAAQ2O;AAGpB,UAAM5I,IAAQ/F,EAAI,QAAQA,EAAI,MAAM,aAAa,IAAK2O,IAAYpE,EAAO,MAAMA,EAAO,WAChFuE,IAAS9O,EAAI,QAAQA,EAAI,MAAM,cAAc,IAAK2O,IAAYpC,EAAQ,QAAQA,EAAQ;AAE5F,UAAIvM,EAAI,MACN,QAAO,QAAQ,QAAQA,EAAI,QAAQA,EAAI,MAAM,WAAW4G,CAAG,IAAIA,CAAG,EAC/D,KAAKA,OAAOb,EAAMa,GAAK5G,CAAG,CAAC,EAC3B,KAAK+G,OAAU/G,EAAI,QAAQA,EAAI,MAAM,iBAAiB+G,CAAM,IAAIA,CAAM,EACtE,KAAKA,OAAU/G,EAAI,aAAa,QAAQ,IAAI,KAAK,WAAW+G,GAAQ/G,EAAI,UAAU,CAAC,EAAE,KAAK,MAAM+G,CAAM,IAAIA,CAAM,EAChH,KAAKA,OAAU+H,EAAO/H,GAAQ/G,CAAG,CAAC,EAClC,KAAKyB,OAAQzB,EAAI,QAAQA,EAAI,MAAM,YAAYyB,CAAI,IAAIA,CAAI,EAC3D,MAAMoN,CAAU;AAGrB,UAAI;AACE7O,UAAI,UACN4G,IAAM5G,EAAI,MAAM,WAAW4G,CAAG;AAEhC,YAAIG,IAAShB,EAAMa,GAAK5G,CAAG;AACvBA,UAAI,UACN+G,IAAS/G,EAAI,MAAM,iBAAiB+G,CAAM,IAExC/G,EAAI,cACN,KAAK,WAAW+G,GAAQ/G,EAAI,UAAU;AAExC,YAAIyB,IAAOqN,EAAO/H,GAAQ/G,CAAG;AAC7B,eAAIA,EAAI,UACNyB,IAAOzB,EAAI,MAAM,YAAYyB,CAAI,IAE5BA;MACT,SAAQsN,GAAG;AACT,eAAOF,EAAWE,CAAU;MAC9B;IACF;EAGF;EAEQ,QAAQC,GAAiBC,GAAgB;AAC/C,WAAQF,OAAuC;AAG7C,UAFAA,EAAE,WAAW;4DAETC,GAAQ;AACV,YAAME,IAAM,mCACRlN,EAAO+M,EAAE,UAAU,IAAI,IAAI,IAC3B;AACJ,eAAIE,IACK,QAAQ,QAAQC,CAAG,IAErBA;MACT;AAEA,UAAID,EACF,QAAO,QAAQ,OAAOF,CAAC;AAEzB,YAAMA;IACR;EACF;AACF;ACjVA,IAAMI,IAAiB,IAAInC;AAqBpB,SAASoC,EAAOxI,IAAa5G,GAAsD;AACxF,SAAOmP,EAAe,MAAMvI,IAAK5G,CAAG;AACtC;AAOAoP,EAAO,UACPA,EAAO,aAAa,SAASzI,IAAwB;AACnD,SAAAwI,EAAe,WAAWxI,EAAO,GACjCyI,EAAO,WAAWD,EAAe,UACjCxP,EAAeyP,EAAO,QAAQ,GACvBA;AACT;AAKAA,EAAO,cAAc3P;AAErB2P,EAAO,WAAW1P;AAMlB0P,EAAO,MAAM,YAAYnC,IAAyB;AAChD,SAAAkC,EAAe,IAAI,GAAGlC,EAAI,GAC1BmC,EAAO,WAAWD,EAAe,UACjCxP,EAAeyP,EAAO,QAAQ,GACvBA;AACT;AAMAA,EAAO,aAAa,SAASrI,IAA8BmG,GAA2D;AACpH,SAAOiC,EAAe,WAAWpI,IAAQmG,CAAQ;AACnD;AASAkC,EAAO,cAAcD,EAAe;AAKpCC,EAAO,SAAS7C;AAChB6C,EAAO,SAAS7C,EAAQ;AACxB6C,EAAO,WAAWjE;AAClBiE,EAAO,eAAe9C;AACtB8C,EAAO,QAAQ7E;AACf6E,EAAO,QAAQ7E,EAAO;AACtB6E,EAAO,YAAY1I;AACnB0I,EAAO,QAAQtC;AACfsC,EAAO,QAAQA;AAER,IAAMzI,KAAUyI,EAAO;AAAvB,IACMC,KAAaD,EAAO;AAD1B,IAEME,KAAMF,EAAO;AAFnB,IAGMX,KAAaW,EAAO;AAH1B,IAIMG,KAAcH,EAAO;AAJ3B,IAKMI,KAAQJ;AALd,IAMMN,KAASvC,EAAQ;AANvB,IAOMxG,KAAQwE,EAAO;", "names": ["_getDefaults", "_defaults", "changeDefaults", "newDefaults", "noopTest", "edit", "regex", "opt", "source", "obj", "name", "val", "valSource", "other", "bull", "indent", "newline", "blockCode", "fences", "hr", "heading", "bullet", "lheadingCore", "lheading", "lheadingGfm", "_paragraph", "blockText", "_blockLabel", "def", "list", "_tag", "_comment", "html", "paragraph", "blockquote", "blockNormal", "gfmTable", "blockGfm", "blockPedantic", "escape", "inlineCode", "br", "inlineText", "_punctuation", "_punctuationOrSpace", "_notPunctuationOrSpace", "punctuation", "_punctuationGfmStrongEm", "_punctuationOrSpaceGfmStrongEm", "_notPunctuationOrSpaceGfmStrongEm", "blockSkip", "emStrongLDelimCore", "em<PERSON><PERSON>g<PERSON><PERSON><PERSON>", "emStrongLDelimGfm", "emStrongRDelimAstCore", "emStrongRDelim<PERSON>t", "emStrongRDelimAstGfm", "emStrongRDelimUnd", "anyPunctuation", "autolink", "_inlineComment", "tag", "_inlineLabel", "link", "reflink", "nolink", "reflinkSearch", "inlineNormal", "inlinePedantic", "inlineGfm", "inlineBreaks", "block", "inline", "escapeReplacements", "getEscapeReplacement", "ch", "encode", "cleanUrl", "href", "splitCells", "tableRow", "count", "row", "match", "offset", "str", "escaped", "curr", "cells", "i", "rtrim", "c", "invert", "l", "suffLen", "curr<PERSON>har", "findClosingBracket", "b", "level", "outputLink", "cap", "raw", "lexer", "rules", "title", "text", "token", "indentCodeCompensation", "matchIndentToCode", "indentToCode", "node", "matchIndentInNode", "indentInNode", "_Tokenizer", "options", "src", "trimmed", "lines", "tokens", "inBlockquote", "currentLines", "currentRaw", "currentText", "top", "lastToken", "oldToken", "newText", "newToken", "isordered", "itemRegex", "endsWithBlankLine", "endEarly", "itemContents", "line", "t", "nextLine", "blankLine", "nextBulletRegex", "hrRegex", "fencesBeginRegex", "headingBeginRegex", "htmlBeginRegex", "rawLine", "nextLineWithoutTabs", "istask", "ischecked", "lastItem", "spacers", "hasMultipleLineBreaks", "headers", "aligns", "rows", "item", "align", "cell", "trimmedUrl", "rtrimSlash", "lastParenIndex", "linkLen", "links", "linkString", "maskedSrc", "prevChar", "l<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "r<PERSON><PERSON><PERSON>", "delimTotal", "midDelimTotal", "endReg", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hasNonSpaceChars", "hasSpaceCharsOnBothEnds", "prevCapZero", "_a", "_<PERSON>er", "__<PERSON><PERSON>", "next", "lastParagraphClipped", "extTokenizer", "cutSrc", "startIndex", "tempSrc", "tempStart", "getStartIndex", "errMsg", "keepPrevChar", "_Renderer", "lang", "langString", "code", "depth", "ordered", "start", "body", "j", "type", "startAttr", "itemBody", "checkbox", "checked", "header", "k", "content", "cleanHref", "out", "_<PERSON><PERSON><PERSON><PERSON>", "_<PERSON><PERSON>r", "__<PERSON><PERSON><PERSON>", "anyToken", "genericToken", "ret", "textToken", "renderer", "_Hooks", "markdown", "Marked", "args", "callback", "values", "tableToken", "listToken", "childTokens", "extensions", "pack", "opts", "ext", "prev<PERSON><PERSON><PERSON>", "extLevel", "prop", "rendererProp", "rendererFunc", "tokenizer", "tokenizerProp", "tokenizerFunc", "prevTokenizer", "hooks", "hooksProp", "hooksFunc", "prevHook", "arg", "walkTokens", "packWalktokens", "blockType", "origOpt", "throwError", "parser", "e", "silent", "async", "msg", "markedInstance", "marked", "setOptions", "use", "parseInline", "parse"]}