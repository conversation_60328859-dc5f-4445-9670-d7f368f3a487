from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
from werkzeug.security import generate_password_hash, check_password_hash

# 创建SQLAlchemy实例时指定编码
db = SQLAlchemy()

class Department(db.Model):
    """科室/病区模型"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    description = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 关系
    nurses = db.relationship('Nurse', backref='department', lazy=True)
    schedules = db.relationship('Schedule', backref='department', lazy=True)
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class Position(db.Model):
    """岗位职责模型"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    description = db.Column(db.Text)
    icon = db.Column(db.String(50))  # 图标名称
    short_name = db.Column(db.String(10))  # 简称
    color = db.Column(db.String(20))  # 颜色
    area = db.Column(db.String(20))  # 区域：东区/西区/通用
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 关系
    nurses = db.relationship('Nurse', secondary='nurse_position', back_populates='positions')
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'icon': self.icon,
            'short_name': self.short_name,
            'color': self.color,
            'area': self.area,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class Nurse(db.Model):
    """护士模型"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    employee_id = db.Column(db.String(50), nullable=False, unique=True)
    department_id = db.Column(db.Integer, db.ForeignKey('department.id'), nullable=False)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    positions = db.relationship('Position', secondary='nurse_position', back_populates='nurses')
    schedules = db.relationship('ScheduleAssignment', backref='nurse', lazy=True)
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'employee_id': self.employee_id,
            'department_id': self.department_id,
            'is_active': self.is_active,
            'positions': [position.to_dict() for position in self.positions],
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class NursePosition(db.Model):
    """护士与岗位多对多关联表"""
    __tablename__ = 'nurse_position'
    nurse_id = db.Column(db.Integer, db.ForeignKey('nurse.id'), primary_key=True)
    position_id = db.Column(db.Integer, db.ForeignKey('position.id'), primary_key=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Schedule(db.Model):
    """排班表模型"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    department_id = db.Column(db.Integer, db.ForeignKey('department.id'), nullable=False)
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    description = db.Column(db.Text)
    is_published = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    assignments = db.relationship('ScheduleAssignment', backref='schedule', lazy=True, cascade='all, delete-orphan')
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'department_id': self.department_id,
            'start_date': self.start_date.isoformat() if self.start_date else None,
            'end_date': self.end_date.isoformat() if self.end_date else None,
            'description': self.description,
            'is_published': self.is_published,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class ScheduleAssignment(db.Model):
    """排班分配模型"""
    id = db.Column(db.Integer, primary_key=True)
    schedule_id = db.Column(db.Integer, db.ForeignKey('schedule.id'), nullable=False)
    nurse_id = db.Column(db.Integer, db.ForeignKey('nurse.id'), nullable=False)
    date = db.Column(db.Date, nullable=False)
    position_id = db.Column(db.Integer, db.ForeignKey('position.id'), nullable=False)
    shift = db.Column(db.String(50))  # 班次 (早班、中班、夜班等)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    position = db.relationship('Position')
    
    def to_dict(self):
        return {
            'id': self.id,
            'schedule_id': self.schedule_id,
            'nurse_id': self.nurse_id,
            'date': self.date.isoformat() if self.date else None,
            'position_id': self.position_id,
            'shift': self.shift,
            'position': self.position.to_dict() if self.position else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class LLMConfig(db.Model):
    """大语言模型配置模型"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    api_base = db.Column(db.String(200), nullable=False)
    api_key = db.Column(db.String(200), nullable=False)
    model = db.Column(db.String(100), nullable=False)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'api_base': self.api_base,
            'model': self.model,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class User(db.Model):
    """用户模型（管理员、护士长等）"""
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    role = db.Column(db.String(50), nullable=False, default='user')  # admin, manager, user
    department_id = db.Column(db.Integer, db.ForeignKey('department.id'), nullable=True)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def to_dict(self):
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'role': self.role,
            'department_id': self.department_id,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class ModelConfig(db.Model):
    """AI模型配置模型"""
    __tablename__ = 'model_config'

    id = db.Column(db.Integer, primary_key=True)
    model_id = db.Column(db.String(50), unique=True, nullable=False)  # 模型标识符
    name = db.Column(db.String(100), nullable=False)  # 模型显示名称
    provider = db.Column(db.String(50), nullable=False)  # 提供商
    api_key = db.Column(db.Text, nullable=False)  # API密钥
    base_url = db.Column(db.String(255), nullable=False)  # API基础URL
    model = db.Column(db.String(100), nullable=False)  # 模型名称
    max_tokens = db.Column(db.Integer, default=4000)  # 最大token数
    temperature = db.Column(db.Float, default=0.7)  # 温度参数
    enabled = db.Column(db.Boolean, default=True)  # 是否启用
    is_default = db.Column(db.Boolean, default=False)  # 是否为默认模型
    description = db.Column(db.Text)  # 模型描述
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def to_dict(self):
        return {
            'id': self.id,
            'model_id': self.model_id,
            'name': self.name,
            'provider': self.provider,
            'api_key': self.api_key[:10] + '...' if self.api_key else '',  # 隐藏API密钥
            'base_url': self.base_url,
            'model': self.model,
            'max_tokens': self.max_tokens,
            'temperature': self.temperature,
            'enabled': self.enabled,
            'is_default': self.is_default,
            'description': self.description,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

    def to_dict_full(self):
        """返回包含完整API密钥的字典（仅用于内部使用）"""
        return {
            'id': self.id,
            'model_id': self.model_id,
            'name': self.name,
            'provider': self.provider,
            'api_key': self.api_key,
            'base_url': self.base_url,
            'model': self.model,
            'max_tokens': self.max_tokens,
            'temperature': self.temperature,
            'enabled': self.enabled,
            'is_default': self.is_default,
            'description': self.description,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
