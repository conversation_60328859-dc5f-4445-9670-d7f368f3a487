#!/usr/bin/env python3
"""
测试新的排班格式输出
验证按照排班.md要求的标准格式
"""
import requests
import json
import time

def test_standard_format_output():
    """测试标准格式输出"""
    print("📋 测试标准格式排班输出...")
    
    # 登录获取token
    login_response = requests.post('http://localhost:5000/api/v1/auth/login', 
                                  json={'username': 'admin', 'password': 'admin123'})
    
    if login_response.status_code != 200:
        print(f"❌ 登录失败: {login_response.status_code}")
        return False
    
    token = login_response.json()['data']['token']
    headers = {'Authorization': f'Bearer {token}', 'Content-Type': 'application/json'}
    print("✅ 登录成功")
    
    # 测试标准格式排班生成
    schedule_data = {
        'department_id': 2,  # 西区
        'requirements': '''请严格按照排班.md的要求生成排班表：
1. 每个工作日安排两个责任护士(◇责1、◇责2)，一个值班护士(值)，一个治疗护士(治)
2. 每个周末安排两个责任护士(◇责1、◇责2)，一个值治护士(值治)
3. 每天安排1个听班护士(听1)
4. 每人每周休息2天(休)
5. 夜班护士(夜)连续两天排班，每人每周一次
6. 请输出标准表格格式和JSON数据''',
        'schedule_name': '标准格式测试排班表',
        'start_date': '2025-08-04',
        'end_date': '2025-08-10',
        'model_id': 'openrouter'  # 使用Kimi K2模型
    }
    
    try:
        response = requests.post('http://localhost:5000/api/v1/schedules/generate-stream', 
                               headers=headers, json=schedule_data, stream=True, timeout=90)
        
        if response.status_code == 200:
            print("✅ 标准格式API调用成功")
            
            full_content = ""
            table_format_found = False
            json_format_found = False
            standard_headers_found = False
            position_symbols_found = False
            
            # 处理流式响应
            for line in response.iter_lines():
                if line:
                    line_str = line.decode('utf-8')
                    if line_str.startswith('data: '):
                        data_str = line_str[6:]
                        if data_str == '[DONE]':
                            break
                        try:
                            data = json.loads(data_str)
                            if 'choices' in data:
                                content = data['choices'][0]['delta'].get('content', '')
                                if content:
                                    full_content += content
                                    
                                    # 检查标准表格格式
                                    if '护士' in content and '08-04(一)' in content:
                                        standard_headers_found = True
                                    
                                    # 检查表格格式
                                    if '|' in content or '\t' in content:
                                        table_format_found = True
                                    
                                    # 检查JSON格式
                                    if '"schedule"' in content and '{' in content:
                                        json_format_found = True
                                    
                                    # 检查岗位符号
                                    if '◇责1' in content or '◇责2' in content or '值' in content or '治' in content:
                                        position_symbols_found = True
                        except json.JSONDecodeError:
                            continue
            
            print(f"✅ 生成内容长度: {len(full_content)} 字符")
            print(f"✅ 包含标准表头: {standard_headers_found}")
            print(f"✅ 包含表格格式: {table_format_found}")
            print(f"✅ 包含JSON格式: {json_format_found}")
            print(f"✅ 包含岗位符号: {position_symbols_found}")
            
            # 显示部分内容
            if full_content:
                print("\n📝 生成内容预览:")
                lines = full_content.split('\n')
                for i, line in enumerate(lines[:20]):  # 显示前20行
                    if line.strip():
                        print(f"  {i+1:2d}: {line}")
                
                if len(lines) > 20:
                    print(f"  ... (还有{len(lines)-20}行)")
            
            # 验证是否符合排班.md要求
            validation_score = 0
            if standard_headers_found:
                validation_score += 1
            if table_format_found:
                validation_score += 1
            if json_format_found:
                validation_score += 1
            if position_symbols_found:
                validation_score += 1
            
            print(f"\n📊 格式验证得分: {validation_score}/4")
            
            if validation_score >= 3:
                print("🎉 排班格式基本符合要求！")
                return True
            else:
                print("⚠️ 排班格式需要改进")
                return False
            
        else:
            print(f"❌ 标准格式API调用失败: {response.status_code}")
            return False
    
    except Exception as e:
        print(f"❌ 标准格式测试异常: {str(e)}")
        return False

def test_chat_mode_with_table():
    """测试对话模式的表格输出"""
    print("\n💬 测试对话模式表格输出...")
    
    # 登录获取token
    login_response = requests.post('http://localhost:5000/api/v1/auth/login', 
                                  json={'username': 'admin', 'password': 'admin123'})
    
    token = login_response.json()['data']['token']
    headers = {'Authorization': f'Bearer {token}', 'Content-Type': 'application/json'}
    
    # 测试对话模式
    chat_data = {
        'department_id': 2,  # 西区
        'requirements': '''你好！请帮我安排西区一周的排班，要求如下：
1. 请严格按照标准格式输出，包含护士姓名和08-04到08-10的日期
2. 使用◇责1、◇责2、值、治、夜、听1、休等标准符号
3. 每人每周休息2天
4. 夜班连续2天
5. 请用表格形式显示，方便查看''',
        'schedule_name': '对话模式表格测试',
        'start_date': '2025-08-04',
        'end_date': '2025-08-10',
        'model_id': 'siliconflow',  # 使用Qwen模型
        'chat_mode': True
    }
    
    try:
        response = requests.post('http://localhost:5000/api/v1/schedules/generate-stream', 
                               headers=headers, json=chat_data, stream=True, timeout=90)
        
        if response.status_code == 200:
            print("✅ 对话模式API调用成功")
            
            full_content = ""
            friendly_tone_found = False
            table_in_markdown = False
            
            # 处理流式响应
            for line in response.iter_lines():
                if line:
                    line_str = line.decode('utf-8')
                    if line_str.startswith('data: '):
                        data_str = line_str[6:]
                        if data_str == '[DONE]':
                            break
                        try:
                            data = json.loads(data_str)
                            if 'choices' in data:
                                content = data['choices'][0]['delta'].get('content', '')
                                if content:
                                    full_content += content
                                    
                                    # 检查友好语气
                                    if any(word in content for word in ['您好', '为您', '帮您', '请查看', '希望']):
                                        friendly_tone_found = True
                                    
                                    # 检查Markdown表格
                                    if '|' in content and ('护士' in content or '◇责' in content):
                                        table_in_markdown = True
                        except json.JSONDecodeError:
                            continue
            
            print(f"✅ 生成内容长度: {len(full_content)} 字符")
            print(f"✅ 包含友好语气: {friendly_tone_found}")
            print(f"✅ 包含Markdown表格: {table_in_markdown}")
            
            return friendly_tone_found and table_in_markdown
            
        else:
            print(f"❌ 对话模式API调用失败: {response.status_code}")
            return False
    
    except Exception as e:
        print(f"❌ 对话模式测试异常: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试新的排班格式功能")
    print("="*60)
    
    results = {}
    
    # 测试标准格式输出
    results['standard_format'] = test_standard_format_output()
    
    # 测试对话模式表格输出
    results['chat_table'] = test_chat_mode_with_table()
    
    # 总结测试结果
    print("\n" + "="*60)
    print("📊 测试结果总结")
    print("="*60)
    
    for test_name, success in results.items():
        status = "✅ 成功" if success else "❌ 失败"
        test_display = {
            'standard_format': '标准格式输出',
            'chat_table': '对话模式表格'
        }
        print(f"{status} {test_display.get(test_name, test_name)}")
    
    success_count = sum(results.values())
    total_count = len(results)
    
    print(f"\n🎯 总体测试结果: {success_count}/{total_count} 通过")
    
    if success_count == total_count:
        print("🎉 新的排班格式功能测试全部通过！")
        print("📋 系统现在支持:")
        print("  - 标准表格格式输出 (护士 + 日期列)")
        print("  - 标准岗位符号 (◇责1、◇责2、值、治、夜、听1、休)")
        print("  - 对话式友好交互")
        print("  - 可编辑表格界面")
    else:
        print("⚠️ 部分功能需要进一步优化")
    
    return success_count == total_count

if __name__ == "__main__":
    main()
