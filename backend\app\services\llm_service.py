from backend.app.models.models import LLMConfig
from backend.app.services.model_config_service import ModelConfigService
from openai import OpenAI
import json

class LLMService:
    """
    大语言模型服务类
    处理与LLM的交互，包括排班表生成等
    """

    def __init__(self, model_id=None):
        """
        初始化LLM服务
        :param model_id: 模型ID，如果为None则使用默认模型
        """
        if model_id:
            # 使用指定的模型配置
            self.config = ModelConfigService.get_model_by_id(model_id)
        else:
            # 使用默认模型配置
            self.config = ModelConfigService.get_default_model()

        if self.config:
            self.client = OpenAI(
                base_url=self.config['base_url'],
                api_key=self.config['api_key']
            )
            self.model = self.config['model']
            self.max_tokens = self.config.get('max_tokens', 4000)
            self.temperature = self.config.get('temperature', 0.7)
            self.model_name = self.config['name']
        else:
            # 使用硬编码的备用配置
            self.client = OpenAI(
                base_url='https://api.siliconflow.cn/v1',
                api_key='sk-siiswpsbpksaraejiwejzaiejlbfrjdysshlnlhpvpqzfruu'
            )
            self.model = "Qwen/Qwen3-235B-A22B-Instruct-2507"
            self.max_tokens = 4000
            self.temperature = 0.7
            self.model_name = "备用模型"
    
    def generate_schedule(self, requirements, nurses, positions, department, start_date, end_date):
        """
        根据需求生成排班表
        :param requirements: 排班需求描述
        :param nurses: 护士列表
        :param positions: 岗位列表
        :param department: 科室信息
        :param start_date: 开始日期
        :param end_date: 结束日期
        :return: 生成的排班表
        """
        # 构建提示词
        prompt = self._build_schedule_prompt(requirements, nurses, positions, department, start_date, end_date)
        
        # 调用LLM生成排班表
        completion = self.client.chat.completions.create(
            model=self.model,
            messages=[
                {"role": "system", "content": f"你是一个专业的护士排班助手（使用{self.model_name}），请根据提供的信息生成合理的排班表。严格按照要求输出标准JSON格式。"},
                {"role": "user", "content": prompt}
            ],
            temperature=self.temperature,
            max_tokens=self.max_tokens,
            stream=False  # 非流式输出
        )
        
        full_content = completion.choices[0].message.content
        
        # 清理可能的Markdown包装和其他文本
        # 移除可能的 "json" 文本前缀
        full_content = full_content.strip()
        if full_content.startswith("json"):
            full_content = full_content[4:].strip()
            
        # 移除可能的Markdown代码块标记
        if full_content.startswith("```"):
            # 找到第一个换行符的位置
            first_newline = full_content.find('\n')
            if first_newline != -1:
                full_content = full_content[first_newline + 1:]
        
        # 移除结尾的代码块标记
        if full_content.endswith("```"):
            full_content = full_content[:-3]
            
        # 再次清理空白字符
        full_content = full_content.strip()
        
        # 尝试解析为JSON格式
        try:
            return json.loads(full_content)
        except json.JSONDecodeError as e:
            # 如果不是JSON格式，抛出异常
            raise Exception(f"LLM返回的内容不是有效的JSON格式: {full_content}") from e
    
    def _build_schedule_prompt(self, requirements, nurses, positions, department, start_date, end_date):
        """
        构建排班提示词 - 根据排班.md标准规则
        """
        nurses_info = "\n".join([f"- {nurse.name} (工号: {nurse.employee_id})" for nurse in nurses])

        # 根据排班.md的要求构建标准提示词
        prompt = f"""你是一个专业的护士排班助手，请根据以下信息和标准规则生成合理的排班表。

**科室信息：**
- 科室名称：{department.name}

**护士信息：**
{nurses_info}

**排班时间：**
从 {start_date} 到 {end_date}

**用户要求：**
{requirements}

**标准排班规则（必须严格遵守）：**
1. 所有护士按照行逐一列出，列表中有护士姓名和周一到周日共8列
2. 每个工作日，安排两个"责任护士"，一个"值班护士"，一个"治疗护士"。每个周末，安排两个"责任护士"，一个"值治护士"
3. 周一到周日每一天，西区安排1个"听班护士"，表示为"听1"
4. 每个人每周需要休息两天，表中标注为"休"，护士休班在任意一天都可以，不需要安排周末
5. "听1"、"听2"视为休班，与休班合并计算每人每周不超过2天，超过两天的部分，改为上值班护士（值）
6. 表中的岗位用括弧中的简称表示：责任护士（责）、辅助责任护士（辅）、值班护士（值）、值班1护士（值1）、治疗护士（治）、夜班护士（夜）、休班（休）
7. 每天的两个责任护士分别用"◇责1"、"◇责2"表示
8. 夜班护士（夜）连续两天排班
9. 夜班护士（夜）每人每周排班一次，一次连续两天
10. 夜班护士（夜）排班后一天，必须至少安排一次休班（休）

**输出格式要求：**
请严格按照以下格式输出排班表：

```
护士	08-04(一)	08-05(二)	08-06(三)	08-07(四)	08-08(五)	08-09(六)	08-10(日)
朱改灵	◇责1	值	休	治	◇责1	休	值治
李公梅	治	◇责2	值	休	夜	夜	休
亓军玲	值	休	◇责1	值	治	◇责1	休
韩师	休	治	休	◇责2	夜	夜	◇责2
邵燕美	◇责2	值	◇责2	休	值	休	听1
张彤	夜	夜	休	值	◇责2	治	休
崔志星	休	听1	夜	夜	休	◇责2	值
宋铜铜	夜	夜	休	听1	休	值	◇责1
王晓力	听1	休	夜	夜	休	休	治
```

**同时提供JSON格式数据：**
```json
{{
  "schedule": {{
    "朱改灵": {{
      "2025-08-04": "◇责1",
      "2025-08-05": "值",
      "2025-08-06": "休",
      "2025-08-07": "治",
      "2025-08-08": "◇责1",
      "2025-08-09": "休",
      "2025-08-10": "值治"
    }}
  }}
}}
```

请确保排班表符合所有规则，先输出文字表格，再输出JSON数据。
"""
        return prompt
    
    def analyze_schedule(self, schedule_data):
        """
        分析排班表，提供统计信息和建议
        :param schedule_data: 排班表数据
        :return: 分析结果
        """
        prompt = f"""
        请分析以下排班表数据并提供统计信息和改进建议：
        
        排班表数据：
        {json.dumps(schedule_data, ensure_ascii=False, indent=2)}
        
        要求：
        1. 统计每位护士的工作天数和班次分布
        2. 检查是否有违反排班规则的情况
        3. 提供改进建议
        4. 严格按照以下标准JSON格式输出结果：
           {{
             "statistics": {{
               "total_days": "总天数",
               "nurse_workload": [
                 {{
                   "nurse_name": "护士姓名",
                   "total_shifts": "总班次",
                   "shift_breakdown": {{ "早班": 数量, "中班": 数量, "夜班": 数量 }}
                 }}
               ]
             }},
             "issues": [
               {{
                 "description": "问题描述"
               }}
             ],
             "suggestions": [
               "改进建议"
             ]
           }}
        5. 不要输出任何其他内容，只输出JSON
        """
        
        # 使用 OpenAI 客户端而不是直接的 HTTP 请求
        completion = self.client.chat.completions.create(
            model=self.model,
            messages=[
                {"role": "system", "content": "你是一个专业的排班分析师，请分析排班表并提供统计信息和改进建议。严格按照要求输出标准JSON格式。"},
                {"role": "user", "content": prompt}
            ],
            temperature=0.3,
            max_tokens=4000,
            stream=False
        )

        try:
            content = completion.choices[0].message.content
            if not content:
                raise Exception("LLM返回的内容为空")

            # 清理可能的Markdown包装
            if content.startswith("```json"):
                content = content[7:]  # 移除开头的 ```json
            if content.endswith("```"):
                content = content[:-3]  # 移除结尾的 ```

            return json.loads(content)
        except (json.JSONDecodeError, KeyError) as e:
            content = ""
            raise Exception(f"LLM返回的内容不是有效的JSON格式: {content}") from e
    
    def generate_schedule_stream(self, requirements, nurses, positions, department, start_date, end_date,
                               chat_mode=False, table_mode=False):
        """
        根据需求生成排班表（流式输出版本）
        :param requirements: 排班需求描述
        :param nurses: 护士列表
        :param positions: 岗位列表
        :param department: 科室信息
        :param start_date: 开始日期
        :param end_date: 结束日期
        :param chat_mode: 是否为对话模式
        :param table_mode: 是否为表格模式
        :return: 生成的排班表（流式）
        """
        # 构建提示词
        if chat_mode:
            prompt = self._build_chat_prompt(requirements, nurses, positions, department, start_date, end_date)
        elif table_mode:
            prompt = self._build_table_prompt(requirements, nurses, positions, department, start_date, end_date)
        else:
            prompt = self._build_schedule_prompt(requirements, nurses, positions, department, start_date, end_date)
        
        # 调用LLM生成排班表（流式）
        stream = self.client.chat.completions.create(
            model=self.model,
            messages=[
                {"role": "system", "content": f"你是一个专业的护士排班助手（使用{self.model_name}），请根据提供的信息生成合理的排班表。严格按照要求输出标准JSON格式。"},
                {"role": "user", "content": prompt}
            ],
            temperature=self.temperature,
            max_tokens=self.max_tokens,
            stream=True  # 使用流式输出
        )

        return stream

    def _build_chat_prompt(self, requirements, nurses, positions, department, start_date, end_date):
        """构建对话模式的提示词"""
        nurses_info = "\n".join([f"- {nurse.name} (工号: {nurse.employee_id})" for nurse in nurses])
        positions_info = "\n".join([f"- {pos.name}: {getattr(pos, 'description', '无描述')}" for pos in positions])

        prompt = f"""你是一个专业的护士排班助手，正在与用户进行对话。请用友好、专业的语气回应用户的排班需求。

**科室信息：**
- 科室名称：{department.name}
- 科室描述：{getattr(department, 'description', '无描述')}

**护士信息：**
{nurses_info}

**可用岗位：**
{positions_info}

**排班时间：**
从 {start_date} 到 {end_date}

**用户需求：**
{requirements}

请根据用户的需求，用Markdown格式回复，包括：
1. 对用户需求的理解和确认
2. 详细的排班建议和说明
3. 如果需要，可以提供Markdown表格格式的排班表
4. 询问用户是否需要调整或有其他要求

请用专业但友好的语气回复，就像一个经验丰富的护士长在与同事讨论排班安排。"""

        return prompt

    def _build_table_prompt(self, requirements, nurses, positions, department, start_date, end_date):
        """构建表格模式的提示词"""
        nurses_info = "\n".join([f"- {nurse.name} (工号: {nurse.employee_id})" for nurse in nurses])
        positions_info = "\n".join([f"- {pos.name}: {getattr(pos, 'description', '无描述')}" for pos in positions])

        prompt = f"""你是一个专业的护士排班助手，需要生成标准的排班表格。

**科室信息：**
- 科室名称：{department.name}
- 科室描述：{getattr(department, 'description', '无描述')}

**护士信息：**
{nurses_info}

**可用岗位：**
{positions_info}

**排班时间：**
从 {start_date} 到 {end_date}

**排班要求：**
{requirements}

请生成一个详细的排班表，包含以下内容：

1. **排班说明**：简要说明排班的原则和考虑因素

2. **排班表格**：使用Markdown表格格式，包含：
   - 护士姓名
   - 每天的具体安排（周一到周日）
   - 使用简称表示岗位（如：责、值、治、夜、听、休等）

3. **JSON格式数据**：在代码块中提供JSON格式的排班数据，格式如下：
```json
{{
  "schedule": {{
    "护士姓名": {{
      "2025-07-28": "责任护士",
      "2025-07-29": "值班护士",
      ...
    }},
    ...
  }}
}}
```

4. **统计信息**：每个护士的工作天数、休息天数等统计

请确保排班合理，符合护理工作规范。"""

        return prompt